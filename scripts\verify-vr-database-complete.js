#!/usr/bin/env node

/**
 * Verify Complete VR Database Schema
 * Task 6.2.1 Verification: Test all 8 VR tables exist and are accessible
 * Date: 2025-07-01
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://hiyqiqbgiueyyvqoqhht.supabase.co';
const SUPABASE_ANON_KEY = 'sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP';

console.log('🔍 VR Database Schema Verification');
console.log('Task: 6.2.1 Verification - Test all VR tables');
console.log('=' * 50);

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test all VR tables exist and are accessible
 */
async function verifyAllVRTables() {
  try {
    console.log('\n📊 Testing all VR tables...');
    
    const allVRTables = [
      'vr_experiences',
      'vr_asset_manifests', 
      'vr_user_preferences',
      'vr_user_saves',
      'vr_shopping_carts',
      'vr_haptic_patterns',
      'vr_audio_zones',
      'vr_update_manifests'
    ];
    
    const results = [];
    
    for (const tableName of allVRTables) {
      try {
        console.log(`   Testing ${tableName}...`);
        
        const { data, error } = await supabase
          .from(tableName)
          .select('id')
          .limit(1);
        
        if (error && error.message.includes('relation') && error.message.includes('does not exist')) {
          results.push({ table: tableName, status: 'MISSING', error: error.message });
          console.log(`   ❌ ${tableName}: MISSING`);
        } else if (error) {
          results.push({ table: tableName, status: 'ERROR', error: error.message });
          console.log(`   ⚠️ ${tableName}: ERROR - ${error.message}`);
        } else {
          results.push({ table: tableName, status: 'EXISTS', error: null });
          console.log(`   ✅ ${tableName}: EXISTS`);
        }
      } catch (err) {
        results.push({ table: tableName, status: 'ERROR', error: err.message });
        console.log(`   ❌ ${tableName}: ERROR - ${err.message}`);
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Error verifying VR tables:', error.message);
    return [];
  }
}

/**
 * Test basic VR operations
 */
async function testVROperations() {
  try {
    console.log('\n🧪 Testing basic VR operations...');
    
    // Test reading from vr_experiences (should exist)
    console.log('   Testing vr_experiences read...');
    const { data: experiences, error: expError } = await supabase
      .from('vr_experiences')
      .select('id, name, status')
      .limit(5);
    
    if (expError) {
      console.log(`   ❌ vr_experiences read failed: ${expError.message}`);
    } else {
      console.log(`   ✅ vr_experiences read successful (${experiences.length} records)`);
    }
    
    // Test reading from vr_shopping_carts (newly created)
    console.log('   Testing vr_shopping_carts read...');
    const { data: carts, error: cartError } = await supabase
      .from('vr_shopping_carts')
      .select('id, user_id')
      .limit(5);
    
    if (cartError) {
      console.log(`   ❌ vr_shopping_carts read failed: ${cartError.message}`);
      return false;
    } else {
      console.log(`   ✅ vr_shopping_carts read successful (${carts.length} records)`);
    }
    
    // Test reading from vr_haptic_patterns (newly created)
    console.log('   Testing vr_haptic_patterns read...');
    const { data: haptics, error: hapticError } = await supabase
      .from('vr_haptic_patterns')
      .select('id, pattern_name')
      .limit(5);
    
    if (hapticError) {
      console.log(`   ❌ vr_haptic_patterns read failed: ${hapticError.message}`);
      return false;
    } else {
      console.log(`   ✅ vr_haptic_patterns read successful (${haptics.length} records)`);
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing VR operations:', error.message);
    return false;
  }
}

/**
 * Generate VR database status report
 */
async function generateStatusReport(results) {
  try {
    console.log('\n📋 VR Database Status Report');
    console.log('-' * 40);
    
    const existingTables = results.filter(r => r.status === 'EXISTS');
    const missingTables = results.filter(r => r.status === 'MISSING');
    const errorTables = results.filter(r => r.status === 'ERROR');
    
    const completionPercentage = Math.round((existingTables.length / 8) * 100);
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total VR Tables: 8`);
    console.log(`   ✅ Existing: ${existingTables.length}`);
    console.log(`   ❌ Missing: ${missingTables.length}`);
    console.log(`   ⚠️ Errors: ${errorTables.length}`);
    console.log(`   📈 Completion: ${completionPercentage}%`);
    
    if (existingTables.length > 0) {
      console.log(`\n✅ Existing Tables:`);
      existingTables.forEach(table => {
        console.log(`   - ${table.table}`);
      });
    }
    
    if (missingTables.length > 0) {
      console.log(`\n❌ Missing Tables:`);
      missingTables.forEach(table => {
        console.log(`   - ${table.table}`);
      });
    }
    
    if (errorTables.length > 0) {
      console.log(`\n⚠️ Tables with Errors:`);
      errorTables.forEach(table => {
        console.log(`   - ${table.table}: ${table.error}`);
      });
    }
    
    const isComplete = missingTables.length === 0 && errorTables.length === 0;
    
    console.log(`\n🎯 Task 6.2.1 Status: ${isComplete ? '✅ COMPLETE' : '⚠️ INCOMPLETE'}`);
    
    if (isComplete) {
      console.log('\n🎉 All VR database tables are ready!');
      console.log('✅ Ready to proceed with VR service deployment');
    } else {
      console.log('\n⚠️ VR database setup incomplete');
      console.log('📝 Please create missing tables manually in Supabase SQL Editor');
    }
    
    return isComplete;
    
  } catch (error) {
    console.error('❌ Error generating status report:', error.message);
    return false;
  }
}

/**
 * Main verification function
 */
async function main() {
  try {
    console.log(`\n🔗 Connecting to Supabase:`);
    console.log(`   URL: ${SUPABASE_URL}`);
    console.log(`   Key: ${SUPABASE_ANON_KEY.substring(0, 20)}...`);
    
    // Verify all VR tables
    const results = await verifyAllVRTables();
    
    if (results.length === 0) {
      console.error('❌ Failed to verify VR tables');
      process.exit(1);
    }
    
    // Test basic operations
    const operationsWork = await testVROperations();
    
    // Generate status report
    const isComplete = await generateStatusReport(results);
    
    if (isComplete && operationsWork) {
      console.log('\n🎉 Task 6.2.1 COMPLETED: VR database schema is complete and functional!');
      console.log('🚀 Ready to proceed with Task 6.2.2: Set up VR asset storage directories');
      process.exit(0);
    } else {
      console.log('\n⚠️ Task 6.2.1 INCOMPLETE: Manual intervention required');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Verification failed:', error.message);
    process.exit(1);
  }
}

// Run the verification
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
