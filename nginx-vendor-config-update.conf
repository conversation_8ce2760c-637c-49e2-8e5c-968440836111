# Updated NGINX Configuration for Simplified Vendor API Endpoints
# This configuration ensures proper routing for both simplified and deprecated vendor endpoints

# Vendor API endpoints (simplified structure)
location /vendor/ {
    limit_req zone=admin burst=30 nodelay;
    
    # Route to vendor API service (port 3001 or wherever vendor-registration.js runs)
    proxy_pass http://127.0.0.1:3001/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Enhanced headers for vendor API
    proxy_set_header X-Vendor-API "simplified";
    proxy_set_header X-API-Version "2.0";
    
    # Timeout settings for vendor operations
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # CORS headers for vendor portal
    add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Vendor-API" always;
    add_header Access-Control-Allow-Credentials "true" always;
    
    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "https://mvs.kanousai.com";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Vendor-API";
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Content-Length 0;
        return 204;
    }
}

# Alternative configuration if vendor service runs on different port
# Uncomment and adjust if needed:
#
# location /vendor/ {
#     limit_req zone=admin burst=30 nodelay;
#     
#     # Route to consolidated vendor service (if using existing service)
#     proxy_pass http://directus_cluster/vendor/;
#     proxy_set_header Host $host;
#     proxy_set_header X-Real-IP $remote_addr;
#     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     proxy_set_header X-Forwarded-Proto $scheme;
#     
#     # Add simplified API indicator
#     proxy_set_header X-Vendor-API "simplified";
# }

# VR API endpoints (future simplification to /vendor/vr/)
# This is for when we implement VR endpoint consolidation under vendor namespace
location /vendor/vr/ {
    limit_req zone=vr_assets burst=50 nodelay;
    
    # Route to VR API service
    proxy_pass http://127.0.0.1:3002/vr/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-VR-Device $vr_device;
    proxy_set_header X-Vendor-API "simplified";
    
    # VR-specific optimizations
    proxy_buffering off;
    proxy_cache off;
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
}

# Deployment Instructions:
# 1. Backup current config: cp /etc/nginx/sites-available/api.mvs.kanousai.com /etc/nginx/sites-available/api.mvs.kanousai.com.backup
# 2. Update vendor location block with above configuration
# 3. Test configuration: nginx -t
# 4. Reload NGINX: systemctl reload nginx
# 5. Verify vendor endpoints work: curl https://api.mvs.kanousai.com/vendor/health
