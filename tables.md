#Issues in the Supabase Database that need fixing

##Errors
| name                   | title                  | level | facing   | categories   | description                                                                                                 | detail                                                                     | remediation                                                                                | metadata                                                     | cache_key                                      |
| ---------------------- | ---------------------- | ----- | -------- | ------------ | ----------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------ | ------------------------------------------------------------ | ---------------------------------------------- |
| rls_disabled_in_public | RLS Disabled in Public | ERROR | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST | Table \`public.product_displays\` is public, but RLS has not been enabled. | https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public | {"name":"product_displays","type":"table","schema":"public"} | rls_disabled_in_public_public_product_displays |

## Warnings
| name                            | title                               | level | facing   | categories   | description                                                                 | detail                                                                                                                                   | remediation                                                                                              | metadata                                                                          | cache_key                                                                                               |
| ------------------------------- | ----------------------------------- | ----- | -------- | ------------ | --------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.get_zones_near_position\` has a role mutable search_path                                                               | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"get_zones_near_position","type":"function","schema":"public"}            | function_search_path_mutable_public_get_zones_near_position_4adf124038e226c1c62ea158ec330487            |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.update_spatial_ui_timestamp\` has a role mutable search_path                                                           | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_spatial_ui_timestamp","type":"function","schema":"public"}        | function_search_path_mutable_public_update_spatial_ui_timestamp_10ff09e0d1433006b865e7959e736c46        |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.cleanup_old_conversation_contexts\` has a role mutable search_path                                                     | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"cleanup_old_conversation_contexts","type":"function","schema":"public"}  | function_search_path_mutable_public_cleanup_old_conversation_contexts_7552ea65c6905cfbc7639a26c0173207  |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.handle_updated_at\` has a role mutable search_path                                                                     | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"handle_updated_at","type":"function","schema":"public"}                  | function_search_path_mutable_public_handle_updated_at_ca67eb68295dc9cc0f733e4028bb8719                  |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.increment_llm_usage\` has a role mutable search_path                                                                   | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"increment_llm_usage","type":"function","schema":"public"}                | function_search_path_mutable_public_increment_llm_usage_18ea3fb243427b961ce24c88b05dbf51                |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.update_ai_conversation_last_active\` has a role mutable search_path                                                    | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_ai_conversation_last_active","type":"function","schema":"public"} | function_search_path_mutable_public_update_ai_conversation_last_active_7072a66acdb217b2e46b98d7a9939734 |
| function_search_path_mutable    | Function Search Path Mutable        | WARN  | EXTERNAL | ["SECURITY"] | Detects functions where the search_path parameter is not set.               | Function \`public.update_updated_at_column\` has a role mutable search_path                                                              | https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable         | {"name":"update_updated_at_column","type":"function","schema":"public"}           | function_search_path_mutable_public_update_updated_at_column_1489083fd9d99d4554c8e29b3e9a7ad3           |
| auth_leaked_password_protection | Leaked Password Protection Disabled | WARN  | EXTERNAL | ["SECURITY"] | Leaked password protection is currently disabled.                           | Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security. | https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection | {"type":"auth","entity":"Auth"}                                                   | auth_leaked_password_protection                                                                         |
| auth_insufficient_mfa_options   | Insufficient MFA Options            | WARN  | EXTERNAL | ["SECURITY"] | This project has too few multi-factor authentication (MFA) options enabled. | Your project has too few MFA options enabled, which may weaken account security. Enable more MFA methods to enhance security.            | https://supabase.com/docs/guides/auth/auth-mfa                                                           | {"type":"auth","entity":"Auth"}                                                   | auth_insufficient_mfa_options                                                                           |

## suggestions
| name                  | title                 | level | facing   | categories   | description                                                                                                     | detail                                                                               | remediation                                                                               | metadata                                                                 | cache_key                                                 |
| --------------------- | --------------------- | ----- | -------- | ------------ | --------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------- | ------------------------------------------------------------------------ | --------------------------------------------------------- |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.ai_product_knowledge\` has RLS enabled, but no policies exist         | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"ai_product_knowledge","type":"table","schema":"public"}         | rls_enabled_no_policy_public_ai_product_knowledge         |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.builds\` has RLS enabled, but no policies exist                       | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"builds","type":"table","schema":"public"}                       | rls_enabled_no_policy_public_builds                       |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_access\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_access","type":"table","schema":"public"}              | rls_enabled_no_policy_public_directus_access              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_activity\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_activity","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_activity            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_collections\` has RLS enabled, but no policies exist         | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_collections","type":"table","schema":"public"}         | rls_enabled_no_policy_public_directus_collections         |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_comments\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_comments","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_comments            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_dashboards\` has RLS enabled, but no policies exist          | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_dashboards","type":"table","schema":"public"}          | rls_enabled_no_policy_public_directus_dashboards          |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_extensions\` has RLS enabled, but no policies exist          | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_extensions","type":"table","schema":"public"}          | rls_enabled_no_policy_public_directus_extensions          |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_fields\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_fields","type":"table","schema":"public"}              | rls_enabled_no_policy_public_directus_fields              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_files\` has RLS enabled, but no policies exist               | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_files","type":"table","schema":"public"}               | rls_enabled_no_policy_public_directus_files               |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_flows\` has RLS enabled, but no policies exist               | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_flows","type":"table","schema":"public"}               | rls_enabled_no_policy_public_directus_flows               |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_folders\` has RLS enabled, but no policies exist             | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_folders","type":"table","schema":"public"}             | rls_enabled_no_policy_public_directus_folders             |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_migrations\` has RLS enabled, but no policies exist          | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_migrations","type":"table","schema":"public"}          | rls_enabled_no_policy_public_directus_migrations          |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_notifications\` has RLS enabled, but no policies exist       | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_notifications","type":"table","schema":"public"}       | rls_enabled_no_policy_public_directus_notifications       |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_operations\` has RLS enabled, but no policies exist          | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_operations","type":"table","schema":"public"}          | rls_enabled_no_policy_public_directus_operations          |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_panels\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_panels","type":"table","schema":"public"}              | rls_enabled_no_policy_public_directus_panels              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_permissions\` has RLS enabled, but no policies exist         | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_permissions","type":"table","schema":"public"}         | rls_enabled_no_policy_public_directus_permissions         |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_policies\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_policies","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_policies            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_presets\` has RLS enabled, but no policies exist             | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_presets","type":"table","schema":"public"}             | rls_enabled_no_policy_public_directus_presets             |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_relations\` has RLS enabled, but no policies exist           | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_relations","type":"table","schema":"public"}           | rls_enabled_no_policy_public_directus_relations           |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_revisions\` has RLS enabled, but no policies exist           | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_revisions","type":"table","schema":"public"}           | rls_enabled_no_policy_public_directus_revisions           |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_roles\` has RLS enabled, but no policies exist               | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_roles","type":"table","schema":"public"}               | rls_enabled_no_policy_public_directus_roles               |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_sessions\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_sessions","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_sessions            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_settings\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_settings","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_settings            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_shares\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_shares","type":"table","schema":"public"}              | rls_enabled_no_policy_public_directus_shares              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_translations\` has RLS enabled, but no policies exist        | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_translations","type":"table","schema":"public"}        | rls_enabled_no_policy_public_directus_translations        |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_users\` has RLS enabled, but no policies exist               | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_users","type":"table","schema":"public"}               | rls_enabled_no_policy_public_directus_users               |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_versions\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_versions","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_versions            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.directus_webhooks\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"directus_webhooks","type":"table","schema":"public"}            | rls_enabled_no_policy_public_directus_webhooks            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.inventory\` has RLS enabled, but no policies exist                    | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"inventory","type":"table","schema":"public"}                    | rls_enabled_no_policy_public_inventory                    |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.layout_items\` has RLS enabled, but no policies exist                 | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"layout_items","type":"table","schema":"public"}                 | rls_enabled_no_policy_public_layout_items                 |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.layout_versions\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"layout_versions","type":"table","schema":"public"}              | rls_enabled_no_policy_public_layout_versions              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.material_types\` has RLS enabled, but no policies exist               | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"material_types","type":"table","schema":"public"}               | rls_enabled_no_policy_public_material_types               |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.materialx_generation_backlog\` has RLS enabled, but no policies exist | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"materialx_generation_backlog","type":"table","schema":"public"} | rls_enabled_no_policy_public_materialx_generation_backlog |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.order_items\` has RLS enabled, but no policies exist                  | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"order_items","type":"table","schema":"public"}                  | rls_enabled_no_policy_public_order_items                  |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.orders\` has RLS enabled, but no policies exist                       | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"orders","type":"table","schema":"public"}                       | rls_enabled_no_policy_public_orders                       |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.payment_transactions\` has RLS enabled, but no policies exist         | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"payment_transactions","type":"table","schema":"public"}         | rls_enabled_no_policy_public_payment_transactions         |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.payments\` has RLS enabled, but no policies exist                     | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"payments","type":"table","schema":"public"}                     | rls_enabled_no_policy_public_payments                     |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.revenue_analytics\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"revenue_analytics","type":"table","schema":"public"}            | rls_enabled_no_policy_public_revenue_analytics            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.rooms\` has RLS enabled, but no policies exist                        | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"rooms","type":"table","schema":"public"}                        | rls_enabled_no_policy_public_rooms                        |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.showroom_layouts\` has RLS enabled, but no policies exist             | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"showroom_layouts","type":"table","schema":"public"}             | rls_enabled_no_policy_public_showroom_layouts             |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.spatial_3d_elements\` has RLS enabled, but no policies exist          | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"spatial_3d_elements","type":"table","schema":"public"}          | rls_enabled_no_policy_public_spatial_3d_elements          |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.spatial_ui_zones\` has RLS enabled, but no policies exist             | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"spatial_ui_zones","type":"table","schema":"public"}             | rls_enabled_no_policy_public_spatial_ui_zones             |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.spatial_zone_interactions\` has RLS enabled, but no policies exist    | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"spatial_zone_interactions","type":"table","schema":"public"}    | rls_enabled_no_policy_public_spatial_zone_interactions    |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.unreal_client_sessions\` has RLS enabled, but no policies exist       | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"unreal_client_sessions","type":"table","schema":"public"}       | rls_enabled_no_policy_public_unreal_client_sessions       |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.upsell_cross_sell\` has RLS enabled, but no policies exist            | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"upsell_cross_sell","type":"table","schema":"public"}            | rls_enabled_no_policy_public_upsell_cross_sell            |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.user_saves\` has RLS enabled, but no policies exist                   | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"user_saves","type":"table","schema":"public"}                   | rls_enabled_no_policy_public_user_saves                   |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.user_sessions\` has RLS enabled, but no policies exist                | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"user_sessions","type":"table","schema":"public"}                | rls_enabled_no_policy_public_user_sessions                |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.users\` has RLS enabled, but no policies exist                        | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"users","type":"table","schema":"public"}                        | rls_enabled_no_policy_public_users                        |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.variation_types\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"variation_types","type":"table","schema":"public"}              | rls_enabled_no_policy_public_variation_types              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vendor_analytics\` has RLS enabled, but no policies exist             | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vendor_analytics","type":"table","schema":"public"}             | rls_enabled_no_policy_public_vendor_analytics             |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vendor_dashboard_metrics\` has RLS enabled, but no policies exist     | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vendor_dashboard_metrics","type":"table","schema":"public"}     | rls_enabled_no_policy_public_vendor_dashboard_metrics     |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vendor_showroom_configs\` has RLS enabled, but no policies exist      | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vendor_showroom_configs","type":"table","schema":"public"}      | rls_enabled_no_policy_public_vendor_showroom_configs      |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vendor_visibility_settings\` has RLS enabled, but no policies exist   | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vendor_visibility_settings","type":"table","schema":"public"}   | rls_enabled_no_policy_public_vendor_visibility_settings   |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vendor_webhooks\` has RLS enabled, but no policies exist              | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vendor_webhooks","type":"table","schema":"public"}              | rls_enabled_no_policy_public_vendor_webhooks              |
| rls_enabled_no_policy | RLS Enabled No Policy | INFO  | EXTERNAL | ["SECURITY"] | Detects cases where row level security (RLS) has been enabled on a table but no RLS policies have been created. | Table \`public.vr_performance_metrics\` has RLS enabled, but no policies exist       | https://supabase.com/docs/guides/database/database-linter?lint=0008_rls_enabled_no_policy | {"name":"vr_performance_metrics","type":"table","schema":"public"}       | rls_enabled_no_policy_public_vr_performance_metrics       |