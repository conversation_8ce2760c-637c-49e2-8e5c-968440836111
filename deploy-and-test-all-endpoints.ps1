# Complete Deployment and Testing of Simplified Vendor API Endpoints
# This script deploys changes and tests ALL endpoints comprehensively

Write-Host "🚀 MVS-VR Vendor API Endpoint Simplification - Complete Deployment & Testing" -ForegroundColor Blue
Write-Host "=" * 80 -ForegroundColor Gray

# Configuration
$SERVER_IP = "**************"
$SERVER_USER = "root"
$API_BASE = "https://api.mvs.kanousai.com"
$VENDOR_BASE = "$API_BASE/vendor"

# Test Results Storage
$TestResults = @()

function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [string]$Description,
        [string]$ExpectedStatus = "200",
        [string]$PostData = $null
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    try {
        if ($Method -eq "POST" -and $PostData) {
            $response = curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -d $PostData $Url
        } else {
            $response = curl -s -w "%{http_code}" $Url
        }
        
        $httpCode = $response[-3..-1] -join ""
        $body = $response[0..($response.Length-4)] -join ""
        
        if ($httpCode -eq $ExpectedStatus) {
            Write-Host "  ✅ PASS - HTTP $httpCode" -ForegroundColor Green
            $script:TestResults += [PSCustomObject]@{
                Endpoint = $Url
                Description = $Description
                Status = "PASS"
                HttpCode = $httpCode
                Response = $body.Substring(0, [Math]::Min(100, $body.Length))
            }
        } else {
            Write-Host "  ❌ FAIL - HTTP $httpCode (expected $ExpectedStatus)" -ForegroundColor Red
            $script:TestResults += [PSCustomObject]@{
                Endpoint = $Url
                Description = $Description
                Status = "FAIL"
                HttpCode = $httpCode
                Response = $body.Substring(0, [Math]::Min(100, $body.Length))
            }
        }
    } catch {
        Write-Host "  ❌ ERROR - $_" -ForegroundColor Red
        $script:TestResults += [PSCustomObject]@{
            Endpoint = $Url
            Description = $Description
            Status = "ERROR"
            HttpCode = "N/A"
            Response = $_.Exception.Message
        }
    }
    Write-Host ""
}

# Step 1: Test Current State (Before Deployment)
Write-Host "`n📋 STEP 1: Testing Current Endpoint State" -ForegroundColor Cyan
Write-Host "-" * 50 -ForegroundColor Gray

Test-Endpoint "$VENDOR_BASE/health" "GET" "Vendor Health (Simplified)" "200"
Test-Endpoint "$VENDOR_BASE/api/register" "POST" "Vendor Register (Old /api/ pattern)" "400" '{"test":"data"}'
Test-Endpoint "$VENDOR_BASE/register" "POST" "Vendor Register (Simplified)" "400" '{"test":"data"}'
Test-Endpoint "$API_BASE/health" "GET" "Main API Health" "200"
Test-Endpoint "$API_BASE/api/status" "GET" "API Status" "200"

# Step 2: Deploy Updated Code (if SSH access available)
Write-Host "`n📤 STEP 2: Deploying Simplified Vendor API" -ForegroundColor Cyan
Write-Host "-" * 50 -ForegroundColor Gray

Write-Host "Attempting to deploy updated vendor-registration.js..." -ForegroundColor Yellow
try {
    # Try to upload the file
    scp app/api/vendor-registration.js ${SERVER_USER}@${SERVER_IP}:/opt/mvs-vr/app/api/ 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ File uploaded successfully" -ForegroundColor Green
        
        # Try to restart service
        ssh ${SERVER_USER}@${SERVER_IP} "cd /opt/mvs-vr && pm2 restart vendor-api || pm2 start app/api/vendor-registration.js --name vendor-api" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Service restarted successfully" -ForegroundColor Green
            Start-Sleep -Seconds 5  # Wait for service to start
        } else {
            Write-Host "⚠️ Service restart failed - manual intervention needed" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ File upload failed - SSH access not configured" -ForegroundColor Yellow
        Write-Host "   Manual deployment required on DO server" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ Deployment skipped - SSH access not available" -ForegroundColor Yellow
    Write-Host "   Please manually deploy app/api/vendor-registration.js to DO server" -ForegroundColor Gray
}

# Step 3: Test All Endpoints After Deployment
Write-Host "`n🧪 STEP 3: Comprehensive Endpoint Testing" -ForegroundColor Cyan
Write-Host "-" * 50 -ForegroundColor Gray

# Core API Endpoints
Write-Host "Testing Core API Endpoints..." -ForegroundColor White
Test-Endpoint "$API_BASE/health" "GET" "Main API Health Check" "200"
Test-Endpoint "$API_BASE/api/status" "GET" "API Status Endpoint" "200"
Test-Endpoint "$API_BASE/server/health" "GET" "Server Health Check" "200"

# Vendor Endpoints - Simplified (New)
Write-Host "Testing Simplified Vendor Endpoints..." -ForegroundColor White
Test-Endpoint "$VENDOR_BASE/health" "GET" "Vendor Health (Simplified)" "200"
Test-Endpoint "$VENDOR_BASE/register" "POST" "Vendor Register (Simplified)" "400" '{"company_name":"Test","email":"<EMAIL>"}'
Test-Endpoint "$VENDOR_BASE/verify" "GET" "Vendor Verify (Simplified)" "400"

# Vendor Endpoints - Deprecated (Old /api/ pattern)
Write-Host "Testing Deprecated Vendor Endpoints..." -ForegroundColor White
Test-Endpoint "$VENDOR_BASE/api/health" "GET" "Vendor Health (Deprecated)" "200"
Test-Endpoint "$VENDOR_BASE/api/register" "POST" "Vendor Register (Deprecated)" "400" '{"company_name":"Test","email":"<EMAIL>"}'
Test-Endpoint "$VENDOR_BASE/api/verify" "GET" "Vendor Verify (Deprecated)" "400"

# Authentication Endpoints
Write-Host "Testing Authentication Endpoints..." -ForegroundColor White
Test-Endpoint "$API_BASE/auth/login" "POST" "Auth Login" "400" '{"email":"<EMAIL>","password":"test"}'
Test-Endpoint "$API_BASE/auth/register" "POST" "Auth Register" "400" '{"email":"<EMAIL>","password":"test"}'

# Admin and Directus Endpoints
Write-Host "Testing Admin and CMS Endpoints..." -ForegroundColor White
Test-Endpoint "$API_BASE/admin" "GET" "Admin Portal Redirect" "301"
Test-Endpoint "$API_BASE/directus/server/health" "GET" "Directus Health" "200"

Write-Host "`n📊 STEP 4: Test Results Summary" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray

$PassCount = ($TestResults | Where-Object { $_.Status -eq "PASS" }).Count
$FailCount = ($TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
$ErrorCount = ($TestResults | Where-Object { $_.Status -eq "ERROR" }).Count
$TotalCount = $TestResults.Count

Write-Host "📈 OVERALL RESULTS:" -ForegroundColor White
Write-Host "   ✅ PASSED: $PassCount/$TotalCount" -ForegroundColor Green
Write-Host "   ❌ FAILED: $FailCount/$TotalCount" -ForegroundColor Red
Write-Host "   ⚠️ ERRORS: $ErrorCount/$TotalCount" -ForegroundColor Yellow

if ($FailCount -eq 0 -and $ErrorCount -eq 0) {
    Write-Host "`n🎉 ALL ENDPOINTS WORKING PERFECTLY!" -ForegroundColor Green
} elseif ($PassCount -gt ($FailCount + $ErrorCount)) {
    Write-Host "`n✅ MAJORITY OF ENDPOINTS WORKING - Minor issues to address" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️ SIGNIFICANT ISSUES DETECTED - Manual intervention required" -ForegroundColor Red
}

Write-Host "`n📋 DETAILED RESULTS:" -ForegroundColor White
$TestResults | Format-Table -AutoSize

Write-Host "`n🔗 SIMPLIFIED ENDPOINT STRUCTURE:" -ForegroundColor Cyan
Write-Host "NEW (Simplified):" -ForegroundColor Green
Write-Host "  POST $VENDOR_BASE/register" -ForegroundColor White
Write-Host "  GET  $VENDOR_BASE/verify" -ForegroundColor White
Write-Host "  GET  $VENDOR_BASE/health" -ForegroundColor White
Write-Host "DEPRECATED (Backward Compatibility):" -ForegroundColor Yellow
Write-Host "  POST $VENDOR_BASE/api/register" -ForegroundColor Gray
Write-Host "  GET  $VENDOR_BASE/api/verify" -ForegroundColor Gray
Write-Host "  GET  $VENDOR_BASE/api/health" -ForegroundColor Gray

Write-Host "`n✅ Endpoint simplification deployment and testing complete!" -ForegroundColor Green
