# Deploy VR Asset Storage Setup to DigitalOcean
# Task 6.2.2: Set up VR asset storage directories on DO server
# Date: 2025-07-01

Write-Host "🗂️ Deploying VR Asset Storage Setup to DigitalOcean" -ForegroundColor Blue
Write-Host "Task: 6.2.2 - Set up VR asset storage directories" -ForegroundColor Blue
Write-Host "=" * 60 -ForegroundColor Gray

# Configuration
$SERVER_IP = "**************"
$SERVER_USER = "root"

Write-Host "`n📤 Step 1: Upload VR asset storage setup script..." -ForegroundColor Yellow
try {
    scp scripts/setup-vr-asset-storage.sh ${SERVER_USER}@${SERVER_IP}:/tmp/
    Write-Host "✅ Script uploaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to upload script: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔧 Step 2: Execute VR asset storage setup on DO server..." -ForegroundColor Yellow
try {
    ssh ${SERVER_USER}@${SERVER_IP} "chmod +x /tmp/setup-vr-asset-storage.sh && /tmp/setup-vr-asset-storage.sh"
    Write-Host "✅ VR asset storage setup completed" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to execute setup script: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Step 3: Verify VR asset storage setup..." -ForegroundColor Yellow
try {
    ssh ${SERVER_USER}@${SERVER_IP} "ls -la /opt/mvs-vr/assets/vr/"
    Write-Host "✅ VR asset storage verification completed" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to verify setup: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n🧹 Step 4: Cleanup temporary files..." -ForegroundColor Yellow
try {
    ssh ${SERVER_USER}@${SERVER_IP} "rm -f /tmp/setup-vr-asset-storage.sh"
    Write-Host "✅ Cleanup completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Cleanup warning: $_" -ForegroundColor Yellow
}

Write-Host "`n✅ Task 6.2.2 COMPLETED: VR asset storage directories deployed!" -ForegroundColor Green
Write-Host "🚀 Ready to proceed with Task 6.2.3: Configure VR environment variables" -ForegroundColor Cyan

Write-Host "`n📋 VR Asset Storage Structure Created:" -ForegroundColor White
Write-Host "   📁 /opt/mvs-vr/assets/vr/" -ForegroundColor Gray
Write-Host "   ├── models/        # 3D model files" -ForegroundColor Gray
Write-Host "   ├── textures/      # Texture files" -ForegroundColor Gray
Write-Host "   ├── audio/         # Audio files" -ForegroundColor Gray
Write-Host "   ├── configs/       # Configuration files" -ForegroundColor Gray
Write-Host "   ├── materials/     # Material definitions" -ForegroundColor Gray
Write-Host "   ├── cache/         # Cached processed assets" -ForegroundColor Gray
Write-Host "   ├── temp/          # Temporary upload processing" -ForegroundColor Gray
Write-Host "   ├── vendors/       # Vendor-specific assets" -ForegroundColor Gray
Write-Host "   ├── experiences/   # Experience-specific assets" -ForegroundColor Gray
Write-Host "   ├── backups/       # Asset backups" -ForegroundColor Gray
Write-Host "   └── archive/       # Archived assets" -ForegroundColor Gray

Write-Host "`n🔧 Management Scripts Created:" -ForegroundColor White
Write-Host "   🧹 /opt/mvs-vr/assets/vr/cleanup.sh  # Clean temp and cache files" -ForegroundColor Gray
Write-Host "   💾 /opt/mvs-vr/assets/vr/backup.sh   # Backup critical assets" -ForegroundColor Gray
Write-Host "   ⚙️ /opt/mvs-vr/assets/vr/config.json # Asset configuration" -ForegroundColor Gray
