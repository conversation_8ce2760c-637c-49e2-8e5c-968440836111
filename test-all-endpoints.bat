@echo off
echo ========================================
echo MVS-VR Complete Endpoint Testing
echo ========================================
echo.

set API_BASE=https://api.mvs.kanousai.com
set VENDOR_BASE=%API_BASE%/vendor

echo [STEP 1] Testing Core API Endpoints
echo ----------------------------------------
echo.

echo Testing Main API Health...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/health
echo.

echo Testing API Status...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/api/status
echo.

echo Testing Server Health...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/server/health
echo.

echo [STEP 2] Testing Simplified Vendor Endpoints (NEW)
echo ----------------------------------------
echo.

echo Testing Vendor Health (Simplified)...
curl -s -w "HTTP %%{http_code}\n" %VENDOR_BASE%/health
echo.

echo Testing Vendor Register (Simplified) - POST...
curl -s -w "HTTP %%{http_code}\n" -X POST -H "Content-Type: application/json" -d "{\"test\":\"data\"}" %VENDOR_BASE%/register
echo.

echo Testing Vendor Verify (Simplified)...
curl -s -w "HTTP %%{http_code}\n" %VENDOR_BASE%/verify
echo.

echo [STEP 3] Testing Deprecated Vendor Endpoints (OLD /api/ pattern)
echo ----------------------------------------
echo.

echo Testing Vendor Health (Deprecated)...
curl -s -w "HTTP %%{http_code}\n" %VENDOR_BASE%/api/health
echo.

echo Testing Vendor Register (Deprecated) - POST...
curl -s -w "HTTP %%{http_code}\n" -X POST -H "Content-Type: application/json" -d "{\"test\":\"data\"}" %VENDOR_BASE%/api/register
echo.

echo Testing Vendor Verify (Deprecated)...
curl -s -w "HTTP %%{http_code}\n" %VENDOR_BASE%/api/verify
echo.

echo [STEP 4] Testing Authentication Endpoints
echo ----------------------------------------
echo.

echo Testing Auth Login...
curl -s -w "HTTP %%{http_code}\n" -X POST -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"test\"}" %API_BASE%/auth/login
echo.

echo Testing Auth Register...
curl -s -w "HTTP %%{http_code}\n" -X POST -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"test\"}" %API_BASE%/auth/register
echo.

echo [STEP 5] Testing Admin and CMS Endpoints
echo ----------------------------------------
echo.

echo Testing Admin Portal Redirect...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/admin
echo.

echo Testing Directus Health...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/directus/server/health
echo.

echo [STEP 6] Testing VR Endpoints (if implemented)
echo ----------------------------------------
echo.

echo Testing VR Health...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/api/vr/health
echo.

echo Testing VR Experiences...
curl -s -w "HTTP %%{http_code}\n" %API_BASE%/api/v1/vr/experiences
echo.

echo ========================================
echo Testing Complete!
echo ========================================
echo.
echo ENDPOINT STRUCTURE:
echo.
echo NEW SIMPLIFIED ENDPOINTS:
echo   POST %VENDOR_BASE%/register
echo   GET  %VENDOR_BASE%/verify
echo   GET  %VENDOR_BASE%/health
echo.
echo DEPRECATED ENDPOINTS (Backward Compatibility):
echo   POST %VENDOR_BASE%/api/register
echo   GET  %VENDOR_BASE%/api/verify
echo   GET  %VENDOR_BASE%/api/health
echo.
echo Check the HTTP status codes above:
echo   200 = Success
echo   301/302 = Redirect (OK for admin)
echo   400 = Bad Request (OK for POST without proper data)
echo   401 = Unauthorized (OK for auth endpoints)
echo   404 = Not Found (indicates endpoint doesn't exist)
echo   500 = Server Error (indicates server issues)
echo.
