# Supabase Manual Table Creation Guide
**Date:** 2025-07-01  
**Phase:** 6.2.1 Completion  
**Status:** 🚀 READY FOR MANUAL EXECUTION  

## 📋 Overview
This guide provides the exact SQL statements to manually create the 4 missing VR tables in Supabase.

**Current Status:**
- ✅ Existing: 4/8 tables (vr_experiences, vr_asset_manifests, vr_user_preferences, vr_user_saves)
- ❌ Missing: 4/8 tables (vr_shopping_carts, vr_haptic_patterns, vr_audio_zones, vr_update_manifests)

## 🎯 Manual Creation Instructions

### Step 1: Access Supabase SQL Editor
1. Go to: https://supabase.com/dashboard/project/hiyqiqbgiueyyvqoqhht
2. Navigate to: **SQL Editor** → **New Query**
3. Copy and paste each SQL block below
4. Execute each block individually

### Step 2: Create Missing Tables

#### Table 1: vr_shopping_carts
```sql
-- VR Shopping Carts Table
-- Stores VR commerce shopping cart data
CREATE TABLE IF NOT EXISTS vr_shopping_carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
    cart_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, vendor_id)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_vr_shopping_carts_user_id ON vr_shopping_carts(user_id);

-- Enable RLS
ALTER TABLE vr_shopping_carts ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Users can access their own VR shopping carts" ON vr_shopping_carts
    FOR ALL USING (user_id = auth.uid() OR auth.role() = 'admin');

-- Updated_at trigger
DROP TRIGGER IF EXISTS update_vr_shopping_carts_updated_at ON vr_shopping_carts;
CREATE TRIGGER update_vr_shopping_carts_updated_at
    BEFORE UPDATE ON vr_shopping_carts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

#### Table 2: vr_haptic_patterns
```sql
-- VR Haptic Patterns Table
-- Stores haptic feedback patterns for VR experiences
CREATE TABLE IF NOT EXISTS vr_haptic_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    pattern_name VARCHAR(255) NOT NULL,
    pattern_data JSONB DEFAULT '{}',
    device_type VARCHAR(100) DEFAULT 'generic',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, pattern_name)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_vr_haptic_patterns_experience_id ON vr_haptic_patterns(experience_id);

-- Enable RLS
ALTER TABLE vr_haptic_patterns ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Access VR haptic patterns through experience ownership" ON vr_haptic_patterns
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_haptic_patterns.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );
```

#### Table 3: vr_audio_zones
```sql
-- VR Audio Zones Table
-- Stores spatial audio zone configurations
CREATE TABLE IF NOT EXISTS vr_audio_zones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    zone_name VARCHAR(255) NOT NULL,
    zone_config JSONB DEFAULT '{}',
    spatial_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, zone_name)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_vr_audio_zones_experience_id ON vr_audio_zones(experience_id);

-- Enable RLS
ALTER TABLE vr_audio_zones ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Access VR audio zones through experience ownership" ON vr_audio_zones
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_audio_zones.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );
```

#### Table 4: vr_update_manifests
```sql
-- VR Update Manifests Table
-- Stores background update manifests for VR experiences
CREATE TABLE IF NOT EXISTS vr_update_manifests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    version VARCHAR(50) NOT NULL,
    update_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, version)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_vr_update_manifests_experience_id ON vr_update_manifests(experience_id);

-- Enable RLS
ALTER TABLE vr_update_manifests ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Access VR update manifests through experience ownership" ON vr_update_manifests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_update_manifests.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );
```

### Step 3: Verification Query
After creating all tables, run this verification query:
```sql
-- Verify all VR tables exist
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename LIKE 'vr_%' 
ORDER BY tablename;
```

Expected result: 8 tables starting with 'vr_'

## 🔍 Warnings & Suggestions

### ⚠️ Critical Issues Identified:

#### 1. **Missing updated_at trigger function**
**Issue:** The `update_updated_at_column()` function may not exist in your Supabase instance.
**Solution:** Run this first before creating any triggers:
```sql
-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

#### 2. **RLS Authentication Dependencies**
**Issue:** RLS policies reference `auth.uid()` and `auth.role()` functions.
**Warning:** These functions only work with Supabase authentication. If using custom auth, policies will fail.
**Verification:** Test with: `SELECT auth.uid(), auth.role();`

#### 3. **Foreign Key Dependencies**
**Issue:** Tables reference `vendors(id)` and `vr_experiences(id)`.
**Required:** Ensure these tables exist before creating VR tables:
```sql
-- Verify dependencies exist
SELECT table_name FROM information_schema.tables
WHERE table_name IN ('vendors', 'vr_experiences')
AND table_schema = 'public';
```

#### 4. **Inconsistent API Key Configuration**
**Issue:** Multiple Supabase key formats found in codebase:
- `sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP` (current)
- JWT format keys in some files
**Recommendation:** Verify current key is active and consistent across all configs.

#### 5. **Missing Service Role Key**
**Issue:** Only anonymous key configured, no service role key for admin operations.
**Impact:** Cannot perform admin-level database operations programmatically.
**Solution:** Add service role key to environment variables.

### 🛡️ Security Recommendations:

#### 1. **Row Level Security (RLS) Validation**
**Current Status:** RLS enabled on all VR tables ✅
**Recommendation:** Test RLS policies with different user roles:
```sql
-- Test as different users
SET LOCAL role 'authenticated';
SET LOCAL request.jwt.claims TO '{"sub": "test-user-id", "role": "authenticated"}';
SELECT * FROM vr_experiences; -- Should only show user's data
```

#### 2. **API Key Rotation**
**Current:** Using same keys since project start
**Recommendation:** Rotate Supabase keys after deployment completion
**Process:** Generate new keys in Supabase dashboard → Update all configs → Test endpoints

#### 3. **Database Connection Security**
**Current:** SSL enabled ✅
**Recommendation:** Verify connection string includes `?sslmode=require`

### 📊 Performance Recommendations:

#### 1. **Index Optimization**
**Current:** Basic indexes created ✅
**Additional Indexes Needed:**
```sql
-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_vr_experiences_vendor_status ON vr_experiences(vendor_id, status);
CREATE INDEX IF NOT EXISTS idx_vr_asset_manifests_type_priority ON vr_asset_manifests(asset_type, priority);
```

#### 2. **Connection Pool Configuration**
**Current:** Pool settings in environment ✅
**Recommendation:** Monitor connection usage and adjust pool size based on load

### 🔧 Implementation Recommendations:
1. **Execute in Order** - Create trigger function first, then tables
2. **Verify Dependencies** - Check vendors and vr_experiences tables exist
3. **Test RLS Policies** - Verify with different user contexts
4. **Monitor Performance** - Add additional indexes if needed
5. **Backup First** - Export existing data before schema changes

## 🧪 Post-Creation Testing
After manual creation, run the verification script:
```bash
node scripts/verify-vr-database-complete.js
```

Expected output: 8/8 tables existing (100% completion)

---
**Status:** 📋 Ready for manual execution in Supabase SQL Editor
