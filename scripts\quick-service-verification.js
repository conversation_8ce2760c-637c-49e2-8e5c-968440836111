#!/usr/bin/env node

/**
 * Quick Service Verification Script
 * Rapid check of critical MVS-VR services
 */

const https = require('https');
const http = require('http');

const SERVICES = {
  'Frontend Landing': 'https://mvs.kanousai.com',
  'Admin Portal': 'https://mvs.kanousai.com/admin',
  'Vendor Portal': 'https://mvs.kanousai.com/vendor',
  'API Health': 'http://**************:3000/health',
  'Directus Health': 'http://**************:8055/server/health',
  'API Gateway': 'https://api.mvs.kanousai.com'
};

async function checkService(name, url) {
  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;
    const startTime = Date.now();
    
    const request = client.get(url, { timeout: 10000 }, (response) => {
      const responseTime = Date.now() - startTime;
      const status = response.statusCode;
      
      resolve({
        name,
        url,
        status,
        responseTime,
        healthy: status >= 200 && status < 400,
        message: `${status} - ${responseTime}ms`
      });
    });
    
    request.on('error', (error) => {
      const responseTime = Date.now() - startTime;
      resolve({
        name,
        url,
        status: 0,
        responseTime,
        healthy: false,
        message: `Error: ${error.message}`
      });
    });
    
    request.on('timeout', () => {
      request.destroy();
      const responseTime = Date.now() - startTime;
      resolve({
        name,
        url,
        status: 0,
        responseTime,
        healthy: false,
        message: 'Timeout'
      });
    });
  });
}

async function runQuickVerification() {
  console.log('🔍 MVS-VR Quick Service Verification');
  console.log('=====================================\n');
  
  const results = [];
  
  for (const [name, url] of Object.entries(SERVICES)) {
    console.log(`Checking ${name}...`);
    const result = await checkService(name, url);
    results.push(result);
    
    const icon = result.healthy ? '✅' : '❌';
    console.log(`${icon} ${name}: ${result.message}\n`);
  }
  
  // Summary
  const healthy = results.filter(r => r.healthy).length;
  const total = results.length;
  const healthPercentage = Math.round((healthy / total) * 100);
  
  console.log('📊 SUMMARY');
  console.log('===========');
  console.log(`Health Score: ${healthPercentage}% (${healthy}/${total})`);
  console.log(`Average Response Time: ${Math.round(results.reduce((sum, r) => sum + r.responseTime, 0) / total)}ms`);
  
  if (healthPercentage < 80) {
    console.log('\n🚨 CRITICAL: System health below 80%');
    console.log('Unhealthy services:');
    results.filter(r => !r.healthy).forEach(r => {
      console.log(`  - ${r.name}: ${r.message}`);
    });
  } else {
    console.log('\n✅ System health is acceptable');
  }
  
  return {
    healthPercentage,
    results,
    timestamp: new Date().toISOString()
  };
}

// Run if called directly
if (require.main === module) {
  runQuickVerification()
    .then(summary => {
      process.exit(summary.healthPercentage >= 80 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Verification failed:', error.message);
      process.exit(1);
    });
}

module.exports = { runQuickVerification };
