#!/bin/bash

# Update NGINX Configuration for VR API Routing
# Task 6.2.5: Update NGINX for VR routing
# Date: 2025-07-01

echo "🌐 NGINX VR API Routing Configuration"
echo "Task: 6.2.5 - Update NGINX for VR routing"
echo "====================================="

# Configuration
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
API_CONFIG_FILE="$NGINX_SITES_DIR/api.mvs.kanousai.com.conf"
VR_CONFIG_FILE="nginx/vr-api-routing.conf"
BACKUP_DIR="/etc/nginx/backups"

echo ""
echo "🔍 Checking NGINX configuration..."

# Check if NGINX is installed
if ! command -v nginx &> /dev/null; then
    echo "❌ NGINX is not installed"
    echo "Installing NGINX..."
    apt-get update
    apt-get install -y nginx
    echo "✅ NGINX installed"
fi

# Check NGINX status
if systemctl is-active --quiet nginx; then
    echo "✅ NGINX is running"
else
    echo "⚠️ NGINX is not running, starting..."
    systemctl start nginx
    echo "✅ NGINX started"
fi

echo ""
echo "📁 Preparing NGINX configuration backup..."

# Create backup directory
mkdir -p "$BACKUP_DIR"
echo "   Created backup directory: $BACKUP_DIR"

# Backup current API configuration
if [ -f "$API_CONFIG_FILE" ]; then
    BACKUP_FILE="$BACKUP_DIR/api.mvs.kanousai.com.conf.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$API_CONFIG_FILE" "$BACKUP_FILE"
    echo "   ✅ Backed up current config: $BACKUP_FILE"
else
    echo "   ⚠️ API config file not found: $API_CONFIG_FILE"
    echo "   Creating new API configuration..."
fi

echo ""
echo "🔧 Adding VR API routing configuration..."

# Check if VR routing already exists
if grep -q "/vendor/vr/" "$API_CONFIG_FILE" 2>/dev/null; then
    echo "   ⚠️ VR routing already exists in NGINX config"
    echo "   Updating existing VR routing configuration..."
    
    # Remove existing VR routing block
    sed -i '/# VR API endpoints/,/^}/d' "$API_CONFIG_FILE"
    echo "   Removed existing VR routing block"
fi

# Add VR routing configuration
echo "   Adding new VR routing configuration..."

# Create temporary file with VR routing
cat > /tmp/vr-routing-insert.conf << 'EOF'

    # VR API endpoints (simplified structure /vendor/vr/)
    location /vendor/vr/ {
        limit_req zone=vr_assets burst=50 nodelay;
        
        proxy_pass http://127.0.0.1:3002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-VR-API "simplified";
        
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 30s;
        
        client_max_body_size 150m;
        client_body_timeout 300s;
        
        add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-VR-API-Key" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "https://mvs.kanousai.com";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-VR-API-Key";
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
        
        access_log /var/log/nginx/vr-api-access.log combined;
        error_log /var/log/nginx/vr-api-error.log warn;
    }

    # VR Health check
    location /vendor/vr/health {
        proxy_pass http://127.0.0.1:3002/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
        proxy_send_timeout 10s;
        proxy_cache off;
        
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
EOF

# Insert VR routing before the closing brace of the server block
if [ -f "$API_CONFIG_FILE" ]; then
    # Find the last closing brace and insert before it
    sed -i '$i\' "$API_CONFIG_FILE"  # Ensure file ends with newline
    sed -i '/^}$/i\' "$API_CONFIG_FILE"  # Add newline before last closing brace
    
    # Insert VR routing configuration
    sed -i '/^}$/i\
# VR API Configuration - Added by Task 6.2.5' "$API_CONFIG_FILE"
    
    # Insert the VR routing content
    sed -i '/# VR API Configuration - Added by Task 6.2.5/r /tmp/vr-routing-insert.conf' "$API_CONFIG_FILE"
    
    echo "   ✅ VR routing configuration added to $API_CONFIG_FILE"
else
    echo "   ❌ API config file not found, cannot add VR routing"
    exit 1
fi

# Clean up temporary file
rm -f /tmp/vr-routing-insert.conf

echo ""
echo "🔧 Adding VR rate limiting configuration..."

# Check if rate limiting zone exists
if ! grep -q "zone=vr_assets" "$API_CONFIG_FILE"; then
    echo "   Adding VR rate limiting zone to http block..."
    
    # Add rate limiting zone to http block (if not already present)
    if grep -q "http {" "$API_CONFIG_FILE"; then
        sed -i '/http {/a\    # VR API rate limiting\n    limit_req_zone $binary_remote_addr zone=vr_assets:10m rate=10r/s;' "$API_CONFIG_FILE"
        echo "   ✅ VR rate limiting zone added"
    else
        echo "   ⚠️ Could not find http block, rate limiting zone not added"
    fi
fi

echo ""
echo "🧪 Testing NGINX configuration..."

# Test NGINX configuration
if nginx -t; then
    echo "✅ NGINX configuration test passed"
else
    echo "❌ NGINX configuration test failed"
    echo "Restoring backup configuration..."
    
    if [ -f "$BACKUP_FILE" ]; then
        cp "$BACKUP_FILE" "$API_CONFIG_FILE"
        echo "✅ Backup configuration restored"
    fi
    
    exit 1
fi

echo ""
echo "🔄 Reloading NGINX configuration..."

# Reload NGINX
if systemctl reload nginx; then
    echo "✅ NGINX configuration reloaded successfully"
else
    echo "❌ Failed to reload NGINX configuration"
    echo "Restoring backup and reloading..."
    
    if [ -f "$BACKUP_FILE" ]; then
        cp "$BACKUP_FILE" "$API_CONFIG_FILE"
        systemctl reload nginx
        echo "✅ Backup configuration restored and reloaded"
    fi
    
    exit 1
fi

echo ""
echo "🔍 Verifying VR routing configuration..."

# Test VR health endpoint (will fail until VR service is running, but should show routing works)
echo "   Testing VR health endpoint routing..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/vendor/vr/health)

if [ "$HTTP_CODE" = "502" ] || [ "$HTTP_CODE" = "503" ]; then
    echo "   ✅ VR routing configured (HTTP $HTTP_CODE - service not running yet)"
elif [ "$HTTP_CODE" = "200" ]; then
    echo "   ✅ VR routing working and service responding (HTTP $HTTP_CODE)"
elif [ "$HTTP_CODE" = "404" ]; then
    echo "   ❌ VR routing not working (HTTP $HTTP_CODE - route not found)"
    exit 1
else
    echo "   ⚠️ VR routing status unclear (HTTP $HTTP_CODE)"
fi

echo ""
echo "📋 NGINX VR Routing Configuration Summary"
echo "========================================"

echo "✅ Task 6.2.5 COMPLETED: NGINX VR routing configured successfully!"
echo ""
echo "📁 Configuration files:"
echo "   Main config: $API_CONFIG_FILE"
echo "   Backup: $BACKUP_FILE"
echo "   VR logs: /var/log/nginx/vr-api-*.log"
echo ""
echo "🌐 VR API endpoints configured:"
echo "   https://api.mvs.kanousai.com/vendor/vr/health"
echo "   https://api.mvs.kanousai.com/vendor/vr/experiences"
echo "   https://api.mvs.kanousai.com/vendor/vr/assets"
echo "   https://api.mvs.kanousai.com/vendor/vr/*"
echo ""
echo "🔧 VR service configuration:"
echo "   Proxy target: http://127.0.0.1:3002/"
echo "   Max file size: 150MB"
echo "   Timeout: 300 seconds"
echo "   Rate limit: 10 requests/second (burst 50)"
echo ""
echo "🚀 Ready to proceed with Task 6.2.6: Deploy VR health service"

exit 0
