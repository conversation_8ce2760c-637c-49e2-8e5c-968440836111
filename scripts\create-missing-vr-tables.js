#!/usr/bin/env node

/**
 * Create Missing VR Tables in Supabase
 * Task 6.2.1 Continuation: Create the 4 missing VR tables
 * Date: 2025-07-01
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://hiyqiqbgiueyyvqoqhht.supabase.co';
const SUPABASE_ANON_KEY = 'sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP';

console.log('🔧 Creating Missing VR Tables');
console.log('Task: 6.2.1 Continuation - Create 4 missing VR tables');
console.log('=' * 60);

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test if we can create tables by trying to insert test data
 */
async function testTableCreation() {
  try {
    console.log('\n🧪 Testing table creation capabilities...');
    
    // Try to access existing tables first
    const { data: existingData, error: existingError } = await supabase
      .from('vr_experiences')
      .select('id')
      .limit(1);
    
    if (existingError) {
      console.log('❌ Cannot access existing VR tables:', existingError.message);
      return false;
    }
    
    console.log('✅ Can access existing VR tables');
    
    // Try to access missing tables to confirm they don't exist
    const missingTables = ['vr_shopping_carts', 'vr_haptic_patterns', 'vr_audio_zones', 'vr_update_manifests'];
    
    for (const tableName of missingTables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);
      
      if (error && error.message.includes('relation') && error.message.includes('does not exist')) {
        console.log(`❌ Confirmed missing: ${tableName}`);
      } else {
        console.log(`✅ Table exists: ${tableName}`);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing table creation:', error.message);
    return false;
  }
}

/**
 * Create missing VR tables using Supabase SQL editor approach
 */
async function createMissingTablesManually() {
  console.log('\n📋 Manual Table Creation Instructions');
  console.log('Since we cannot create tables programmatically with the anon key,');
  console.log('please create the following tables manually in Supabase SQL Editor:');
  console.log('');
  
  const missingTableSQL = `
-- VR Shopping Carts Table
CREATE TABLE IF NOT EXISTS vr_shopping_carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
    cart_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, vendor_id)
);

-- VR Haptic Patterns Table
CREATE TABLE IF NOT EXISTS vr_haptic_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    pattern_name VARCHAR(255) NOT NULL,
    pattern_data JSONB DEFAULT '{}',
    device_type VARCHAR(100) DEFAULT 'generic',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, pattern_name)
);

-- VR Audio Zones Table
CREATE TABLE IF NOT EXISTS vr_audio_zones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    zone_name VARCHAR(255) NOT NULL,
    zone_config JSONB DEFAULT '{}',
    spatial_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, zone_name)
);

-- VR Update Manifests Table
CREATE TABLE IF NOT EXISTS vr_update_manifests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    version VARCHAR(50) NOT NULL,
    update_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, version)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_vr_shopping_carts_user_id ON vr_shopping_carts(user_id);
CREATE INDEX IF NOT EXISTS idx_vr_haptic_patterns_experience_id ON vr_haptic_patterns(experience_id);
CREATE INDEX IF NOT EXISTS idx_vr_audio_zones_experience_id ON vr_audio_zones(experience_id);
CREATE INDEX IF NOT EXISTS idx_vr_update_manifests_experience_id ON vr_update_manifests(experience_id);

-- Create updated_at trigger for shopping carts
CREATE TRIGGER update_vr_shopping_carts_updated_at
    BEFORE UPDATE ON vr_shopping_carts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE vr_shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_haptic_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_audio_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_update_manifests ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access their own VR shopping carts" ON vr_shopping_carts
    FOR ALL USING (user_id = auth.uid() OR auth.role() = 'admin');

CREATE POLICY "Access VR haptic patterns through experience ownership" ON vr_haptic_patterns
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_haptic_patterns.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

CREATE POLICY "Access VR audio zones through experience ownership" ON vr_audio_zones
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_audio_zones.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

CREATE POLICY "Access VR update manifests through experience ownership" ON vr_update_manifests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_update_manifests.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );
`;

  console.log(missingTableSQL);
  
  console.log('\n📝 Instructions:');
  console.log('1. Go to https://supabase.com/dashboard/project/hiyqiqbgiueyyvqoqhht/sql');
  console.log('2. Copy and paste the SQL above into the SQL Editor');
  console.log('3. Click "Run" to execute the SQL');
  console.log('4. Verify all 4 tables are created successfully');
  console.log('');
  
  return false; // Manual creation required
}

/**
 * Verify all VR tables exist after manual creation
 */
async function verifyAllVRTables() {
  try {
    console.log('\n🔍 Verifying all VR tables...');
    
    const allVRTables = [
      'vr_experiences',
      'vr_asset_manifests', 
      'vr_user_preferences',
      'vr_user_saves',
      'vr_shopping_carts',
      'vr_haptic_patterns',
      'vr_audio_zones',
      'vr_update_manifests'
    ];
    
    const existingTables = [];
    const missingTables = [];
    
    for (const tableName of allVRTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('id')
          .limit(1);
        
        if (error && error.message.includes('relation') && error.message.includes('does not exist')) {
          missingTables.push(tableName);
        } else {
          existingTables.push(tableName);
        }
      } catch (err) {
        missingTables.push(tableName);
      }
    }
    
    const completionPercentage = Math.round((existingTables.length / 8) * 100);
    
    console.log(`\n📊 VR Schema Status:`);
    console.log(`   Completion: ${completionPercentage}% (${existingTables.length}/8 tables)`);
    console.log(`   ✅ Existing: ${existingTables.join(', ')}`);
    
    if (missingTables.length > 0) {
      console.log(`   ❌ Missing: ${missingTables.join(', ')}`);
    }
    
    const isComplete = missingTables.length === 0;
    console.log(`   Status: ${isComplete ? '✅ COMPLETE' : '⚠️ INCOMPLETE'}`);
    
    return isComplete;
    
  } catch (error) {
    console.error('❌ Error verifying VR tables:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    // Test current capabilities
    const canCreateTables = await testTableCreation();
    
    if (!canCreateTables) {
      console.log('\n❌ Cannot create tables programmatically with current permissions');
      await createMissingTablesManually();
      process.exit(1);
    }
    
    // Verify current status
    const isComplete = await verifyAllVRTables();
    
    if (isComplete) {
      console.log('\n🎉 Task 6.2.1 COMPLETED: All VR database tables exist!');
      process.exit(0);
    } else {
      console.log('\n⚠️ Task 6.2.1 INCOMPLETE: Manual table creation required');
      await createMissingTablesManually();
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
