{"timestamp": "2025-06-30T22:47:36.342Z", "summary": {"total": 21, "passed": 16, "failed": 5, "warnings": 1, "critical_failures": 3}, "categories": {"public": {"status": "passed", "tests": [{"name": "GET /health", "status": "passed", "result": {"url": "http://**************:3000/health", "method": "GET", "statusCode": 200, "responseTime": 431, "attempts": 1, "headers": {"x-powered-by": "Express", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "84", "etag": "W/\"54-xQg350alEiUUZhA+xQ4LAgA/JUU\"", "date": "Mon, 30 Jun 2025 22:47:36 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 84}}, {"name": "GET /api/status", "status": "passed", "result": {"url": "http://**************:3000/api/status", "method": "GET", "statusCode": 200, "responseTime": 210, "attempts": 1, "headers": {"x-powered-by": "Express", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "171", "etag": "W/\"ab-HXlFuxHgHXtgzG74HngFLAQKq+o\"", "date": "Mon, 30 Jun 2025 22:47:36 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 171}}, {"name": "GET /api/vendors", "status": "passed", "result": {"url": "http://**************:3000/api/vendors", "method": "GET", "statusCode": 200, "responseTime": 210, "attempts": 1, "headers": {"x-powered-by": "Express", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "196", "etag": "W/\"c4-zEcx3TArm4GAKCeMUjRPI6fVhWY\"", "date": "Mon, 30 Jun 2025 22:47:37 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 196}}, {"name": "GET /api/products", "status": "passed", "result": {"url": "http://**************:3000/api/products", "method": "GET", "statusCode": 200, "responseTime": 210, "attempts": 1, "headers": {"x-powered-by": "Express", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "213", "etag": "W/\"d5-iYyyodhW5ovk2Qi+CZuFwZtlPT4\"", "date": "Mon, 30 Jun 2025 22:47:37 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 213}}], "issues": []}, "auth": {"status": "failed", "tests": [{"name": "POST /auth/login", "status": "failed", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 400 or 401"}, {"name": "POST /auth/register", "status": "failed", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 400 or 409"}, {"name": "POST /auth/logout", "status": "failed", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 401"}, {"name": "POST /auth/refresh", "status": "failed", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 401"}], "issues": [{"endpoint": "/auth/login", "method": "POST", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 400 or 401", "critical": true}, {"endpoint": "/auth/register", "method": "POST", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 400 or 409", "critical": true}, {"endpoint": "/auth/logout", "method": "POST", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 401", "critical": false}, {"endpoint": "/auth/refresh", "method": "POST", "error": "Endpoint test failed after 3 attempts: Unexpected status 404, expected 200 or 401", "critical": true}]}, "admin": {"status": "passed", "tests": [{"name": "GET /admin", "status": "passed", "result": {"url": "https://mvs.kanousai.com/admin", "method": "GET", "statusCode": 200, "responseTime": 130, "attempts": 1, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1306536", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline", "content-length": "6682", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"c518c758fab00cd07d91f47fcda0dde5\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/admin", "x-nextjs-prerender": "1", "x-nextjs-stale-time": "300", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::9ndzm-1751323676156-7b8cc736e4cf", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 6678}}, {"name": "GET /admin/login", "status": "passed", "result": {"url": "https://mvs.kanousai.com/admin/login", "method": "GET", "statusCode": 404, "responseTime": 17, "attempts": 1, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1177487", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline; filename=\"404\"", "content-length": "7954", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"27789380214e978c80b3e818becbaf26\"", "last-modified": "<PERSON><PERSON>, 17 Jun 2025 07:43:08 GMT", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/404", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::jcf8h-1751323676205-bc016812cd22", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 7954}}, {"name": "GET /admin/dashboard", "status": "passed", "result": {"url": "https://mvs.kanousai.com/admin/dashboard", "method": "GET", "statusCode": 404, "responseTime": 11, "attempts": 1, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1177487", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline; filename=\"404\"", "content-length": "7954", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"27789380214e978c80b3e818becbaf26\"", "last-modified": "<PERSON><PERSON>, 17 Jun 2025 07:43:08 GMT", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/404", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::jcf8h-1751323676221-503116ca<PERSON>bad", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 7954}}], "issues": []}, "vendor": {"status": "passed", "tests": [{"name": "GET /vendor", "status": "passed", "result": {"url": "https://mvs.kanousai.com/vendor", "method": "GET", "statusCode": 200, "responseTime": 11, "attempts": 1, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1306529", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline", "content-length": "6687", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"1e0afdecf19a079f2b551daf58cbc935\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/vendor", "x-nextjs-prerender": "1", "x-nextjs-stale-time": "300", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::jcf8h-1751323676235-499e82d8e551", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 6685}}, {"name": "GET /vendor/login", "status": "passed", "result": {"url": "https://mvs.kanousai.com/vendor/login", "method": "GET", "statusCode": 404, "responseTime": 10, "attempts": 1, "headers": {"access-control-allow-origin": "*", "age": "1177487", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline; filename=\"404\"", "content-length": "7954", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"27789380214e978c80b3e818becbaf26\"", "last-modified": "<PERSON><PERSON>, 17 Jun 2025 07:43:08 GMT", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/404", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::jcf8h-1751323676247-b39de9f1c286", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 7954}}, {"name": "GET /vendor/dashboard", "status": "passed", "result": {"url": "https://mvs.kanousai.com/vendor/dashboard", "method": "GET", "statusCode": 404, "responseTime": 20, "attempts": 1, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1177488", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline; filename=\"404\"", "content-length": "7954", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "etag": "\"27789380214e978c80b3e818becbaf26\"", "last-modified": "<PERSON><PERSON>, 17 Jun 2025 07:43:08 GMT", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/404", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::f2zbb-1751323676256-277e2d0443c7", "x-xss-protection": "1; mode=block"}, "contentType": "text/html; charset=utf-8", "contentLength": 7954}}], "issues": []}, "directus": {"status": "passed", "tests": [{"name": "GET /server/health", "status": "passed", "result": {"url": "http://**************:8055/server/health", "method": "GET", "statusCode": 200, "responseTime": 471, "attempts": 1, "headers": {"content-security-policy": "script-src 'self' 'unsafe-eval';worker-src 'self' blob:;child-src 'self' blob:;img-src 'self' data: blob: https://raw.githubusercontent.com https://avatars.githubusercontent.com;media-src 'self';connect-src 'self' https://* wss://*;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "x-powered-by": "Directus", "vary": "Origin, Cache-Control", "access-control-allow-credentials": "true", "access-control-expose-headers": "Content-Range", "content-type": "application/health+json; charset=utf-8", "cache-control": "no-cache", "content-length": "15", "etag": "W/\"f-VaSQ4oDUiZblZNAEkkN+sX+q3Sg\"", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/health+json; charset=utf-8", "contentLength": 15}}, {"name": "GET /server/info", "status": "passed", "result": {"url": "http://**************:8055/server/info", "method": "GET", "statusCode": 200, "responseTime": 253, "attempts": 1, "headers": {"content-security-policy": "script-src 'self' 'unsafe-eval';worker-src 'self' blob:;child-src 'self' blob:;img-src 'self' data: blob: https://raw.githubusercontent.com https://avatars.githubusercontent.com;media-src 'self';connect-src 'self' https://* wss://*;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "x-powered-by": "Directus", "vary": "Origin, Cache-Control", "access-control-allow-credentials": "true", "access-control-expose-headers": "Content-Range", "cache-control": "no-cache", "content-type": "application/json; charset=utf-8", "content-length": "197", "etag": "W/\"c5-vF0aiUvfFeXW9rvXVHB9Xvi743Y\"", "date": "Mon, 30 Jun 2025 22:47:56 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 197}}, {"name": "GET /users/me", "status": "passed", "result": {"url": "http://**************:8055/users/me", "method": "GET", "statusCode": 401, "responseTime": 230, "attempts": 1, "headers": {"content-security-policy": "script-src 'self' 'unsafe-eval';worker-src 'self' blob:;child-src 'self' blob:;img-src 'self' data: blob: https://raw.githubusercontent.com https://avatars.githubusercontent.com;media-src 'self';connect-src 'self' https://* wss://*;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "x-powered-by": "Directus", "vary": "Origin", "access-control-allow-credentials": "true", "access-control-expose-headers": "Content-Range", "content-type": "application/json; charset=utf-8", "content-length": "96", "etag": "W/\"60-SpvBqFAbsdy4SkXwsevzfPClFZA\"", "date": "Mon, 30 Jun 2025 22:47:57 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "contentType": "application/json; charset=utf-8", "contentLength": 96}}], "issues": []}, "cors": {"status": "passed", "tests": [{"name": "CORS - http://**************:3000/health", "status": "passed", "result": {"statusCode": 204, "corsHeaders": {"access-control-allow-origin": "*", "access-control-allow-methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "access-control-allow-headers": "Content-Type"}, "valid": true}}, {"name": "CORS - http://**************:3000/api/status", "status": "passed", "result": {"statusCode": 204, "corsHeaders": {"access-control-allow-origin": "*", "access-control-allow-methods": "GET,HEAD,PUT,PATCH,POST,DELETE", "access-control-allow-headers": "Content-Type"}, "valid": true}}], "issues": []}, "security": {"status": "failed", "tests": [{"name": "Security Headers - https://mvs.kanousai.com", "status": "passed", "result": {"statusCode": 200, "securityHeaders": {"x-frame-options": "DENY", "x-content-type-options": "nosniff", "strict-transport-security": "max-age=31536000; includeSubDomains; preload"}, "issues": [], "warnings": ["Security risk - header present: server: Vercel"], "score": 100}}, {"name": "Security Headers - https://api.mvs.kanousai.com", "status": "failed", "error": "Security header issues: Missing required security header: x-frame-options, Missing required security header: x-content-type-options, Missing required security header: strict-transport-security"}], "issues": [{"url": "https://api.mvs.kanousai.com", "error": "Security header issues: Missing required security header: x-frame-options, Missing required security header: x-content-type-options, Missing required security header: strict-transport-security", "critical": false}]}}, "performance": {"averageResponseTime": 170, "slowestEndpoint": {"name": "GET /server/health", "responseTime": 471, "url": "http://**************:8055/server/health"}, "fastestEndpoint": {"name": "GET /vendor/login", "responseTime": 10, "url": "https://mvs.kanousai.com/vendor/login"}}, "recommendations": [{"category": "Critical Endpoints", "priority": "HIGH", "message": "Critical endpoint failures detected", "actions": ["Review failed critical endpoints immediately", "Check service availability and configuration", "Verify network connectivity and firewall rules"]}, {"category": "Security Headers", "priority": "HIGH", "message": "Security header issues detected", "actions": ["Implement missing security headers", "Review web server configuration", "Consider using security middleware"]}, {"category": "Endpoint Reliability", "priority": "MEDIUM", "message": "Endpoint success rate is 76.2%", "actions": ["Investigate failed endpoint tests", "Implement endpoint monitoring", "Create endpoint health dashboards"]}], "system_info": {"node_version": "v22.14.0", "platform": "win32", "timestamp": "2025-06-30T22:47:58.587Z"}, "test_configuration": {"base_urls": {"api": "https://api.mvs.kanousai.com", "frontend": "https://mvs.kanousai.com", "local_api": "http://**************:3000", "local_directus": "http://**************:8055"}, "total_endpoints": 17, "timeout": 15000, "retries": 3}}