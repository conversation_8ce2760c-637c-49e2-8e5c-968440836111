#!/bin/bash

# Setup VR Asset Storage Directories
# Task 6.2.2: Set up VR asset storage directories
# Date: 2025-07-01

echo "🗂️ VR Asset Storage Setup"
echo "Task: 6.2.2 - Set up VR asset storage directories"
echo "=================================================="

# Configuration
VR_ASSET_BASE="/opt/mvs-vr/assets/vr"
WEB_USER="www-data"
WEB_GROUP="www-data"

echo ""
echo "📁 Creating VR asset directory structure..."

# Create main VR assets directory
echo "   Creating base directory: $VR_ASSET_BASE"
sudo mkdir -p "$VR_ASSET_BASE"

# Create subdirectories for different asset types
echo "   Creating asset type subdirectories..."
sudo mkdir -p "$VR_ASSET_BASE/models"        # 3D model files (.fbx, .obj, .gltf)
sudo mkdir -p "$VR_ASSET_BASE/textures"      # Texture files (.png, .jpg, .dds)
sudo mkdir -p "$VR_ASSET_BASE/audio"         # Audio files (.wav, .ogg, .mp3)
sudo mkdir -p "$VR_ASSET_BASE/configs"       # Configuration files (.json)
sudo mkdir -p "$VR_ASSET_BASE/materials"     # Material definitions (.json)
sudo mkdir -p "$VR_ASSET_BASE/cache"         # Cached processed assets
sudo mkdir -p "$VR_ASSET_BASE/temp"          # Temporary upload processing

# Create vendor-specific subdirectories
echo "   Creating vendor-specific directories..."
sudo mkdir -p "$VR_ASSET_BASE/vendors"       # Vendor-specific asset storage

# Create experience-specific subdirectories
echo "   Creating experience-specific directories..."
sudo mkdir -p "$VR_ASSET_BASE/experiences"   # Experience-specific assets

# Create backup and archive directories
echo "   Creating backup and archive directories..."
sudo mkdir -p "$VR_ASSET_BASE/backups"       # Asset backups
sudo mkdir -p "$VR_ASSET_BASE/archive"       # Archived assets

echo ""
echo "🔒 Setting directory permissions..."

# Set ownership to web server user
echo "   Setting ownership to $WEB_USER:$WEB_GROUP..."
sudo chown -R "$WEB_USER:$WEB_GROUP" "$VR_ASSET_BASE"

# Set directory permissions (755 for directories, 644 for files)
echo "   Setting directory permissions (755)..."
sudo find "$VR_ASSET_BASE" -type d -exec chmod 755 {} \;

echo "   Setting file permissions (644)..."
sudo find "$VR_ASSET_BASE" -type f -exec chmod 644 {} \;

# Set special permissions for upload and temp directories
echo "   Setting special permissions for upload directories..."
sudo chmod 775 "$VR_ASSET_BASE/temp"
sudo chmod 775 "$VR_ASSET_BASE/cache"

echo ""
echo "📝 Creating asset configuration files..."

# Create asset configuration file
cat > /tmp/vr-asset-config.json << 'EOF'
{
  "vr_asset_storage": {
    "base_path": "/opt/mvs-vr/assets/vr",
    "max_file_size": "150MB",
    "allowed_extensions": {
      "models": [".fbx", ".obj", ".gltf", ".glb", ".dae"],
      "textures": [".png", ".jpg", ".jpeg", ".dds", ".tga", ".bmp"],
      "audio": [".wav", ".ogg", ".mp3", ".flac", ".aac"],
      "configs": [".json", ".xml", ".yaml", ".yml"],
      "materials": [".json", ".mtl", ".mat"]
    },
    "cache_duration": 3600,
    "preload_enabled": true,
    "compression_enabled": true,
    "backup_enabled": true,
    "directories": {
      "models": "/opt/mvs-vr/assets/vr/models",
      "textures": "/opt/mvs-vr/assets/vr/textures",
      "audio": "/opt/mvs-vr/assets/vr/audio",
      "configs": "/opt/mvs-vr/assets/vr/configs",
      "materials": "/opt/mvs-vr/assets/vr/materials",
      "cache": "/opt/mvs-vr/assets/vr/cache",
      "temp": "/opt/mvs-vr/assets/vr/temp",
      "vendors": "/opt/mvs-vr/assets/vr/vendors",
      "experiences": "/opt/mvs-vr/assets/vr/experiences",
      "backups": "/opt/mvs-vr/assets/vr/backups",
      "archive": "/opt/mvs-vr/assets/vr/archive"
    }
  }
}
EOF

# Move config file to proper location
sudo mv /tmp/vr-asset-config.json "$VR_ASSET_BASE/config.json"
sudo chown "$WEB_USER:$WEB_GROUP" "$VR_ASSET_BASE/config.json"
sudo chmod 644 "$VR_ASSET_BASE/config.json"

echo ""
echo "📊 Creating asset management scripts..."

# Create asset cleanup script
cat > /tmp/cleanup-vr-assets.sh << 'EOF'
#!/bin/bash
# VR Asset Cleanup Script
# Cleans up temporary files and old cached assets

VR_ASSET_BASE="/opt/mvs-vr/assets/vr"

echo "🧹 Cleaning VR asset storage..."

# Clean temp directory (files older than 1 hour)
echo "   Cleaning temp files..."
find "$VR_ASSET_BASE/temp" -type f -mmin +60 -delete

# Clean cache directory (files older than 24 hours)
echo "   Cleaning cache files..."
find "$VR_ASSET_BASE/cache" -type f -mmin +1440 -delete

# Clean empty directories
echo "   Cleaning empty directories..."
find "$VR_ASSET_BASE" -type d -empty -delete

echo "✅ VR asset cleanup complete"
EOF

sudo mv /tmp/cleanup-vr-assets.sh "$VR_ASSET_BASE/cleanup.sh"
sudo chown "$WEB_USER:$WEB_GROUP" "$VR_ASSET_BASE/cleanup.sh"
sudo chmod 755 "$VR_ASSET_BASE/cleanup.sh"

# Create asset backup script
cat > /tmp/backup-vr-assets.sh << 'EOF'
#!/bin/bash
# VR Asset Backup Script
# Creates backups of critical VR assets

VR_ASSET_BASE="/opt/mvs-vr/assets/vr"
BACKUP_DIR="$VR_ASSET_BASE/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "💾 Creating VR asset backup..."

# Create backup directory for this session
mkdir -p "$BACKUP_DIR/$DATE"

# Backup models (critical assets)
echo "   Backing up models..."
cp -r "$VR_ASSET_BASE/models" "$BACKUP_DIR/$DATE/" 2>/dev/null || true

# Backup configs (critical configurations)
echo "   Backing up configs..."
cp -r "$VR_ASSET_BASE/configs" "$BACKUP_DIR/$DATE/" 2>/dev/null || true

# Backup materials (critical material definitions)
echo "   Backing up materials..."
cp -r "$VR_ASSET_BASE/materials" "$BACKUP_DIR/$DATE/" 2>/dev/null || true

# Create backup manifest
echo "   Creating backup manifest..."
cat > "$BACKUP_DIR/$DATE/manifest.json" << EOL
{
  "backup_date": "$DATE",
  "backup_type": "vr_assets",
  "directories_backed_up": ["models", "configs", "materials"],
  "backup_size": "$(du -sh $BACKUP_DIR/$DATE | cut -f1)"
}
EOL

echo "✅ VR asset backup complete: $BACKUP_DIR/$DATE"
EOF

sudo mv /tmp/backup-vr-assets.sh "$VR_ASSET_BASE/backup.sh"
sudo chown "$WEB_USER:$WEB_GROUP" "$VR_ASSET_BASE/backup.sh"
sudo chmod 755 "$VR_ASSET_BASE/backup.sh"

echo ""
echo "🔍 Verifying VR asset storage setup..."

# Verify all directories exist
DIRECTORIES=(
    "$VR_ASSET_BASE"
    "$VR_ASSET_BASE/models"
    "$VR_ASSET_BASE/textures"
    "$VR_ASSET_BASE/audio"
    "$VR_ASSET_BASE/configs"
    "$VR_ASSET_BASE/materials"
    "$VR_ASSET_BASE/cache"
    "$VR_ASSET_BASE/temp"
    "$VR_ASSET_BASE/vendors"
    "$VR_ASSET_BASE/experiences"
    "$VR_ASSET_BASE/backups"
    "$VR_ASSET_BASE/archive"
)

echo "   Checking directory structure..."
ALL_EXIST=true
for dir in "${DIRECTORIES[@]}"; do
    if [ -d "$dir" ]; then
        echo "   ✅ $dir"
    else
        echo "   ❌ $dir (MISSING)"
        ALL_EXIST=false
    fi
done

# Check permissions
echo ""
echo "   Checking permissions..."
OWNER=$(stat -c '%U:%G' "$VR_ASSET_BASE")
PERMS=$(stat -c '%a' "$VR_ASSET_BASE")

if [ "$OWNER" = "$WEB_USER:$WEB_GROUP" ]; then
    echo "   ✅ Ownership: $OWNER"
else
    echo "   ❌ Ownership: $OWNER (should be $WEB_USER:$WEB_GROUP)"
    ALL_EXIST=false
fi

if [ "$PERMS" = "755" ]; then
    echo "   ✅ Permissions: $PERMS"
else
    echo "   ⚠️ Permissions: $PERMS (should be 755)"
fi

# Check disk space
echo ""
echo "   Checking disk space..."
DISK_USAGE=$(df -h "$VR_ASSET_BASE" | tail -1)
echo "   📊 Disk usage: $DISK_USAGE"

echo ""
echo "📋 VR Asset Storage Setup Summary"
echo "================================="

if [ "$ALL_EXIST" = true ]; then
    echo "✅ Task 6.2.2 COMPLETED: VR asset storage directories created successfully!"
    echo ""
    echo "📁 Directory Structure:"
    echo "   Base: $VR_ASSET_BASE"
    echo "   Asset Types: models, textures, audio, configs, materials"
    echo "   Working: cache, temp"
    echo "   Organization: vendors, experiences"
    echo "   Maintenance: backups, archive"
    echo ""
    echo "🔧 Management Scripts:"
    echo "   Cleanup: $VR_ASSET_BASE/cleanup.sh"
    echo "   Backup: $VR_ASSET_BASE/backup.sh"
    echo "   Config: $VR_ASSET_BASE/config.json"
    echo ""
    echo "🚀 Ready to proceed with Task 6.2.3: Configure VR environment variables"
    exit 0
else
    echo "❌ Task 6.2.2 FAILED: Some directories or permissions are incorrect"
    echo "⚠️ Please check the errors above and run the script again"
    exit 1
fi
