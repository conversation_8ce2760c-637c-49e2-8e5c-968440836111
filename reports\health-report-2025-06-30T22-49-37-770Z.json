{"timestamp": "2025-06-30T22:48:21.979Z", "summary": {"total": 11, "healthy": 7, "unhealthy": 4, "critical_failures": 3}, "services": [{"name": "API Server", "type": "http", "healthy": true, "responseTime": 454, "attempts": 1, "statusCode": 200, "response": {"status": "ok", "timestamp": "2025-06-30T22:48:22.350Z", "service": "mvs-vr-api-server"}, "headers": {"x-powered-by": "Express", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "84", "etag": "W/\"54-bM3M7/MjmL8KbPCk8roQvBNKL6A\"", "date": "Mon, 30 Jun 2025 22:48:22 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, {"name": "Directus CMS", "type": "http", "healthy": true, "responseTime": 475, "attempts": 1, "statusCode": 200, "response": {"status": "ok"}, "headers": {"content-security-policy": "script-src 'self' 'unsafe-eval';worker-src 'self' blob:;child-src 'self' blob:;img-src 'self' data: blob: https://raw.githubusercontent.com https://avatars.githubusercontent.com;media-src 'self';connect-src 'self' https://* wss://*;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "x-powered-by": "Directus", "vary": "Origin, Cache-Control", "access-control-allow-credentials": "true", "access-control-expose-headers": "Content-Range", "content-type": "application/health+json; charset=utf-8", "cache-control": "no-cache", "content-length": "15", "etag": "W/\"f-VaSQ4oDUiZblZNAEkkN+sX+q3Sg\"", "date": "Mon, 30 Jun 2025 22:48:22 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, {"name": "<PERSON><PERSON>", "type": "tcp", "healthy": false, "responseTime": 25051, "attempts": 3, "error": "TCP connection timeout"}, {"name": "Nginx Proxy", "type": "http", "healthy": false, "responseTime": 11396, "attempts": 3, "error": "HTTP 301: Expected 200 or 404"}, {"name": "Prometheus", "type": "http", "healthy": true, "responseTime": 429, "attempts": 1, "statusCode": 200, "response": "Prometheus Server is Healthy.\n", "headers": {"date": "Mon, 30 Jun 2025 22:48:59 GMT", "content-length": "30", "content-type": "text/plain; charset=utf-8"}}, {"name": "<PERSON><PERSON>", "type": "http", "healthy": false, "responseTime": 11277, "attempts": 2, "error": "HTTP request failed: connect ECONNREFUSED **************:3001"}], "endpoints": [{"name": "Frontend Landing", "url": "https://mvs.kanousai.com", "healthy": true, "responseTime": 88, "attempts": 1, "statusCode": 200, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1265491", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline", "content-length": "9337", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:49:11 GMT", "etag": "\"7b592463d8f4941735e2c35c482f8f76\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/", "x-nextjs-prerender": "1", "x-nextjs-stale-time": "300", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::zwj7r-1751323751146-be60f0f99e76", "x-xss-protection": "1; mode=block"}, "contentLength": 9337, "redirect": null}, {"name": "Admin Portal", "url": "https://mvs.kanousai.com/admin", "healthy": true, "responseTime": 11, "attempts": 1, "statusCode": 200, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1306611", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline", "content-length": "6682", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:49:11 GMT", "etag": "\"c518c758fab00cd07d91f47fcda0dde5\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/admin", "x-nextjs-prerender": "1", "x-nextjs-stale-time": "300", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::zwj7r-1751323751166-3a8dcb1e5b52", "x-xss-protection": "1; mode=block"}, "contentLength": 6678, "redirect": null}, {"name": "Vendor Portal", "url": "https://mvs.kanousai.com/vendor", "healthy": true, "responseTime": 11, "attempts": 1, "statusCode": 200, "headers": {"accept-ranges": "bytes", "access-control-allow-origin": "*", "age": "1306604", "cache-control": "public, max-age=0, must-revalidate", "content-disposition": "inline", "content-length": "6687", "content-type": "text/html; charset=utf-8", "date": "Mon, 30 Jun 2025 22:49:11 GMT", "etag": "\"1e0afdecf19a079f2b551daf58cbc935\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "on", "x-frame-options": "DENY", "x-matched-path": "/vendor", "x-nextjs-prerender": "1", "x-nextjs-stale-time": "300", "x-vercel-cache": "HIT", "x-vercel-id": "dxb1::zwj7r-1751323751177-d979d1a83fd2", "x-xss-protection": "1; mode=block"}, "contentLength": 6685, "redirect": null}, {"name": "API Endpoint", "url": "https://api.mvs.kanousai.com", "healthy": true, "responseTime": 669, "attempts": 1, "statusCode": 200, "headers": {"server": "nginx/1.18.0 (Ubuntu)", "date": "Mon, 30 Jun 2025 22:49:11 GMT", "content-type": "application/octet-stream", "content-length": "105", "connection": "keep-alive"}, "contentLength": 105, "redirect": null}], "database": {"name": "Supabase Database", "host": "db.hiyqiqbgiueyyvqoqhht.supabase.co", "port": 5432, "healthy": false, "responseTime": 10050, "attempts": 3, "error": "getaddrinfo ENOTFOUND db.hiyqiqbgiueyyvqoqhht.supabase.co"}, "alerts": [{"type": "service_failure", "service": "<PERSON><PERSON>", "message": "TCP connection timeout", "critical": true, "timestamp": "2025-06-30T22:48:47.959Z"}, {"type": "service_failure", "service": "Nginx Proxy", "message": "HTTP 301: Expected 200 or 404", "critical": true, "timestamp": "2025-06-30T22:48:59.355Z"}, {"type": "service_failure", "service": "<PERSON><PERSON>", "message": "HTTP request failed: connect ECONNREFUSED **************:3001", "critical": false, "timestamp": "2025-06-30T22:49:11.061Z"}, {"type": "database_failure", "database": "Supabase Database", "message": "getaddrinfo ENOTFOUND db.hiyqiqbgiueyyvqoqhht.supabase.co", "critical": true, "timestamp": "2025-06-30T22:49:21.892Z"}], "system_info": {"node_version": "v22.14.0", "platform": "win32", "uptime": 75.8208005, "memory_usage": {"rss": 43335680, "heapTotal": 7528448, "heapUsed": 6197464, "external": 2044011, "arrayBuffers": 19900}}, "configuration": {"check_intervals": {"healthCheck": 30000, "fullCheck": 300000, "retry": 5000}, "total_services": 6, "total_endpoints": 4}}