# Deploy Simplified Vendor API to DigitalOcean
# This script deploys the updated vendor-registration.js with simplified endpoints

Write-Host "🚀 Deploying Simplified Vendor API to DigitalOcean..." -ForegroundColor Blue

# Configuration
$SERVER_IP = "**************"
$SERVER_USER = "root"
$REMOTE_PATH = "/opt/mvs-vr"
$SERVICE_NAME = "vendor-api"

# Step 1: Upload the updated vendor-registration.js
Write-Host "`n📤 Step 1: Uploading updated vendor-registration.js..." -ForegroundColor Yellow
try {
    scp app/api/vendor-registration.js ${SERVER_USER}@${SERVER_IP}:${REMOTE_PATH}/app/api/
    Write-Host "✅ File uploaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to upload file: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Restart the vendor API service
Write-Host "`n🔄 Step 2: Restarting vendor API service..." -ForegroundColor Yellow
try {
    ssh ${SERVER_USER}@${SERVER_IP} "cd ${REMOTE_PATH}; pm2 restart ${SERVICE_NAME} || pm2 start app/api/vendor-registration.js --name ${SERVICE_NAME}"
    Write-Host "✅ Service restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart service: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Test the simplified endpoints
Write-Host "`n🧪 Step 3: Testing simplified endpoints on DO..." -ForegroundColor Yellow

Write-Host "Testing /vendor/health (simplified)..."
$healthTest = curl -s https://api.mvs.kanousai.com/vendor/health
if ($healthTest) {
    Write-Host "✅ /vendor/health works" -ForegroundColor Green
} else {
    Write-Host "❌ /vendor/health failed" -ForegroundColor Red
}

Write-Host "Testing /vendor/api/health (deprecated)..."
$deprecatedTest = curl -s https://api.mvs.kanousai.com/vendor/api/health
if ($deprecatedTest -match "deprecated") {
    Write-Host "✅ /vendor/api/health shows deprecation warning" -ForegroundColor Green
} else {
    Write-Host "⚠️ /vendor/api/health may not show deprecation warning" -ForegroundColor Yellow
}

# Step 4: Check service logs
Write-Host "`n📋 Step 4: Checking service logs..." -ForegroundColor Yellow
ssh ${SERVER_USER}@${SERVER_IP} "pm2 logs ${SERVICE_NAME} --lines 10"

Write-Host "`n✅ Deployment complete! Simplified vendor endpoints are now active on DO." -ForegroundColor Green
Write-Host "📋 New endpoints:" -ForegroundColor Cyan
Write-Host "   POST https://api.mvs.kanousai.com/vendor/register" -ForegroundColor White
Write-Host "   GET  https://api.mvs.kanousai.com/vendor/verify" -ForegroundColor White
Write-Host "   GET  https://api.mvs.kanousai.com/vendor/health" -ForegroundColor White
Write-Host "⚠️  Deprecated (backward compatibility):" -ForegroundColor Yellow
Write-Host "   POST https://api.mvs.kanousai.com/vendor/api/register" -ForegroundColor Gray
Write-Host "   GET  https://api.mvs.kanousai.com/vendor/api/verify" -ForegroundColor Gray
Write-Host "   GET  https://api.mvs.kanousai.com/vendor/api/health" -ForegroundColor Gray
