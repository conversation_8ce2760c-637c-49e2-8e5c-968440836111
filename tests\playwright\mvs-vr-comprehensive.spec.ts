import { test, expect } from '@playwright/test';

/**
 * MVS-VR Comprehensive Frontend Testing Suite
 * Tests all frontend applications and critical user flows
 */

test.describe('MVS-VR Frontend Applications', () => {
  
  test.describe('Landing Page Tests', () => {
    test('should load landing page successfully', async ({ page }) => {
      await page.goto('/');
      
      // Check page loads
      await expect(page).toHaveTitle(/MVS/);
      
      // Check for key elements
      await expect(page.locator('body')).toBeVisible();
      
      // Check response time
      const startTime = Date.now();
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds
    });

    test('should have proper meta tags and SEO', async ({ page }) => {
      await page.goto('/');
      
      // Check meta tags
      const title = await page.locator('title').textContent();
      expect(title).toBeTruthy();
      
      // Check viewport meta tag
      const viewport = page.locator('meta[name="viewport"]');
      await expect(viewport).toHaveAttribute('content', /width=device-width/);
    });

    test('should be responsive on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/');
      
      await expect(page.locator('body')).toBeVisible();
      
      // Check mobile-specific elements or layouts
      const bodyWidth = await page.locator('body').boundingBox();
      expect(bodyWidth?.width).toBeLessThanOrEqual(375);
    });
  });

  test.describe('Admin Portal Tests', () => {
    test('should load admin portal', async ({ page }) => {
      await page.goto('/admin');
      
      // Should either show admin interface or redirect to login
      await page.waitForLoadState('networkidle');
      
      // Check if we're on admin page or login page
      const currentUrl = page.url();
      expect(currentUrl).toMatch(/(admin|login)/);
    });

    test('should handle admin authentication flow', async ({ page }) => {
      await page.goto('/admin');
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Look for login form or admin dashboard
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0;
      const hasAdminDashboard = await page.locator('[data-testid="admin-dashboard"], .admin-dashboard').count() > 0;
      
      expect(hasLoginForm || hasAdminDashboard).toBeTruthy();
    });

    test('should have proper security headers', async ({ page }) => {
      const response = await page.goto('/admin');
      
      // Check security headers
      const headers = response?.headers();
      expect(headers?.['x-frame-options']).toBeTruthy();
      expect(headers?.['x-content-type-options']).toBe('nosniff');
    });
  });

  test.describe('Vendor Portal Tests', () => {
    test('should load vendor portal', async ({ page }) => {
      await page.goto('/vendor');
      
      await page.waitForLoadState('networkidle');
      
      // Should either show vendor interface or redirect to login
      const currentUrl = page.url();
      expect(currentUrl).toMatch(/(vendor|login)/);
    });

    test('should handle vendor registration flow', async ({ page }) => {
      await page.goto('/vendor');
      
      await page.waitForLoadState('networkidle');
      
      // Look for registration or login options
      const hasRegistration = await page.locator('text=register, text=sign up').count() > 0;
      const hasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0;
      const hasVendorDashboard = await page.locator('[data-testid="vendor-dashboard"], .vendor-dashboard').count() > 0;
      
      expect(hasRegistration || hasLogin || hasVendorDashboard).toBeTruthy();
    });

    test('should be accessible', async ({ page }) => {
      await page.goto('/vendor');
      
      // Basic accessibility checks
      const hasHeadings = await page.locator('h1, h2, h3').count() > 0;
      expect(hasHeadings).toBeTruthy();
      
      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        for (let i = 0; i < imageCount; i++) {
          const img = images.nth(i);
          const alt = await img.getAttribute('alt');
          const ariaLabel = await img.getAttribute('aria-label');
          expect(alt || ariaLabel).toBeTruthy();
        }
      }
    });
  });

  test.describe('API Integration Tests', () => {
    test('should handle API connectivity', async ({ page }) => {
      // Test API endpoints through frontend
      await page.goto('/');
      
      // Monitor network requests
      const apiRequests: string[] = [];
      page.on('request', request => {
        if (request.url().includes('api.mvs.kanousai.com') || request.url().includes('/api/')) {
          apiRequests.push(request.url());
        }
      });
      
      await page.waitForLoadState('networkidle');
      
      // Check if any API calls were made
      console.log('API requests detected:', apiRequests.length);
    });

    test('should handle API errors gracefully', async ({ page }) => {
      // Intercept API calls and simulate errors
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        });
      });
      
      await page.goto('/');
      
      // Page should still load even with API errors
      await expect(page.locator('body')).toBeVisible();
    });
  });

  test.describe('Performance Tests', () => {
    test('should meet performance benchmarks', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Performance assertions
      expect(loadTime).toBeLessThan(10000); // Should load within 10 seconds
      
      // Check for performance metrics
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
        };
      });
      
      console.log('Performance metrics:', performanceMetrics);
      
      // Assert performance thresholds
      expect(performanceMetrics.domContentLoaded).toBeLessThan(3000);
      expect(performanceMetrics.firstContentfulPaint).toBeLessThan(2000);
    });

    test('should handle concurrent users', async ({ browser }) => {
      // Simulate multiple concurrent users
      const contexts = await Promise.all([
        browser.newContext(),
        browser.newContext(),
        browser.newContext()
      ]);
      
      const pages = await Promise.all(contexts.map(context => context.newPage()));
      
      // Load pages concurrently
      const loadPromises = pages.map(page => page.goto('/'));
      await Promise.all(loadPromises);
      
      // Verify all pages loaded successfully
      for (const page of pages) {
        await expect(page.locator('body')).toBeVisible();
      }
      
      // Cleanup
      await Promise.all(contexts.map(context => context.close()));
    });
  });

  test.describe('Security Tests', () => {
    test('should have HTTPS redirect', async ({ page }) => {
      // Test HTTP to HTTPS redirect
      const response = await page.goto('http://mvs.kanousai.com', { waitUntil: 'networkidle' });
      
      // Should redirect to HTTPS
      expect(page.url()).toMatch(/^https:/);
    });

    test('should prevent XSS attacks', async ({ page }) => {
      await page.goto('/');
      
      // Try to inject script
      const xssPayload = '<script>window.xssTest = true;</script>';
      
      // Look for input fields to test
      const inputs = page.locator('input[type="text"], textarea');
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        await inputs.first().fill(xssPayload);
        
        // Check that script didn't execute
        const xssExecuted = await page.evaluate(() => (window as any).xssTest);
        expect(xssExecuted).toBeFalsy();
      }
    });

    test('should have proper CSP headers', async ({ page }) => {
      const response = await page.goto('/');
      
      const headers = response?.headers();
      const csp = headers?.['content-security-policy'];
      
      if (csp) {
        expect(csp).toContain('default-src');
      }
    });
  });
});
