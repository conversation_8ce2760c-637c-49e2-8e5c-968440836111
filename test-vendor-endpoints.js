#!/usr/bin/env node

/**
 * Quick test for simplified vendor endpoints
 * Tests the new /vendor/register structure vs old /vendor/api/register
 */

const express = require('express');
const app = express();

// Middleware
app.use(express.json());

// Test the simplified routes (what we implemented)
app.post('/register', (req, res) => {
  console.log('✅ NEW SIMPLIFIED ENDPOINT: /register called');
  res.json({ 
    success: true, 
    message: 'New simplified endpoint working!',
    endpoint: '/register',
    deprecated: false
  });
});

app.get('/verify', (req, res) => {
  console.log('✅ NEW SIMPLIFIED ENDPOINT: /verify called');
  res.json({ 
    success: true, 
    message: 'New simplified endpoint working!',
    endpoint: '/verify',
    deprecated: false
  });
});

app.get('/health', (req, res) => {
  console.log('✅ NEW SIMPLIFIED ENDPOINT: /health called');
  res.json({ 
    status: 'ok', 
    service: 'vendor-registration-api',
    endpoint: '/health',
    deprecated: false
  });
});

// Test the deprecated routes (backward compatibility)
app.post('/api/register', (req, res) => {
  console.log('⚠️ DEPRECATED ENDPOINT: /api/register called');
  res.json({ 
    success: true, 
    message: 'Deprecated endpoint - please use /register instead',
    endpoint: '/api/register',
    deprecated: true,
    newEndpoint: '/register'
  });
});

app.get('/api/verify', (req, res) => {
  console.log('⚠️ DEPRECATED ENDPOINT: /api/verify called');
  res.json({ 
    success: true, 
    message: 'Deprecated endpoint - please use /verify instead',
    endpoint: '/api/verify',
    deprecated: true,
    newEndpoint: '/verify'
  });
});

app.get('/api/health', (req, res) => {
  console.log('⚠️ DEPRECATED ENDPOINT: /api/health called');
  res.json({ 
    status: 'ok', 
    service: 'vendor-registration-api',
    endpoint: '/api/health',
    deprecated: true,
    newEndpoint: '/health'
  });
});

const PORT = 3002;
app.listen(PORT, () => {
  console.log(`🧪 Testing Vendor Endpoint Simplification on port ${PORT}`);
  console.log(`\n📋 NEW SIMPLIFIED ENDPOINTS:`);
  console.log(`   POST http://localhost:${PORT}/register`);
  console.log(`   GET  http://localhost:${PORT}/verify`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`\n⚠️  DEPRECATED ENDPOINTS (backward compatibility):`);
  console.log(`   POST http://localhost:${PORT}/api/register`);
  console.log(`   GET  http://localhost:${PORT}/api/verify`);
  console.log(`   GET  http://localhost:${PORT}/api/health`);
  console.log(`\n🔗 Test with: curl http://localhost:${PORT}/health`);
});
