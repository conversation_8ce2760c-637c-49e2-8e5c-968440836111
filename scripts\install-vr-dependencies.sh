#!/bin/bash

# Install VR Service Dependencies
# Task 6.2.4: Install VR service dependencies
# Date: 2025-07-01

echo "📦 VR Service Dependencies Installation"
echo "Task: 6.2.4 - Install VR service dependencies"
echo "=========================================="

# Configuration
VR_SERVICE_DIR="/opt/mvs-vr/server/api/vr"
NODE_VERSION="18"
NPM_VERSION="8"

echo ""
echo "🔍 Checking system requirements..."

# Check Node.js version
if command -v node &> /dev/null; then
    NODE_CURRENT=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    echo "   Node.js version: $(node --version)"
    
    if [ "$NODE_CURRENT" -ge "$NODE_VERSION" ]; then
        echo "   ✅ Node.js version requirement met (>= $NODE_VERSION)"
    else
        echo "   ❌ Node.js version too old (need >= $NODE_VERSION)"
        echo "   Installing Node.js $NODE_VERSION..."
        
        # Install Node.js via NodeSource repository
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
        
        echo "   ✅ Node.js $NODE_VERSION installed"
    fi
else
    echo "   ❌ Node.js not found, installing..."
    
    # Install Node.js
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    echo "   ✅ Node.js installed"
fi

# Check npm version
if command -v npm &> /dev/null; then
    NPM_CURRENT=$(npm --version | cut -d'.' -f1)
    echo "   npm version: $(npm --version)"
    
    if [ "$NPM_CURRENT" -ge "$NPM_VERSION" ]; then
        echo "   ✅ npm version requirement met (>= $NPM_VERSION)"
    else
        echo "   ⚠️ npm version could be updated"
        npm install -g npm@latest
        echo "   ✅ npm updated to latest version"
    fi
else
    echo "   ❌ npm not found (should be installed with Node.js)"
    exit 1
fi

# Check PM2
if command -v pm2 &> /dev/null; then
    echo "   ✅ PM2 already installed: $(pm2 --version)"
else
    echo "   Installing PM2 process manager..."
    npm install -g pm2
    echo "   ✅ PM2 installed"
fi

echo ""
echo "📁 Setting up VR service directory..."

# Create VR service directory if it doesn't exist
if [ ! -d "$VR_SERVICE_DIR" ]; then
    echo "   Creating VR service directory: $VR_SERVICE_DIR"
    mkdir -p "$VR_SERVICE_DIR"
    chown -R www-data:www-data "$VR_SERVICE_DIR"
else
    echo "   ✅ VR service directory exists: $VR_SERVICE_DIR"
fi

# Navigate to VR service directory
cd "$VR_SERVICE_DIR" || exit 1

echo ""
echo "📋 Installing VR service dependencies..."

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "   ❌ package.json not found in $VR_SERVICE_DIR"
    echo "   Please ensure VR service files are deployed first"
    exit 1
fi

echo "   Found package.json, installing dependencies..."

# Install production dependencies
echo "   Installing production dependencies..."
npm ci --only=production --silent

if [ $? -eq 0 ]; then
    echo "   ✅ Production dependencies installed successfully"
else
    echo "   ❌ Failed to install production dependencies"
    exit 1
fi

# Install development dependencies (for development environment)
if [ "$NODE_ENV" = "development" ]; then
    echo "   Installing development dependencies..."
    npm ci --silent
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Development dependencies installed successfully"
    else
        echo "   ⚠️ Failed to install development dependencies (continuing...)"
    fi
fi

echo ""
echo "🔧 Setting up VR service utilities..."

# Create log directories
echo "   Creating log directories..."
mkdir -p /var/log/pm2
mkdir -p /var/log/mvs-vr
chown -R www-data:www-data /var/log/mvs-vr
chmod 755 /var/log/mvs-vr

# Create VR scripts directory
echo "   Creating VR scripts directory..."
mkdir -p /opt/mvs-vr/scripts
chown -R www-data:www-data /opt/mvs-vr/scripts

# Set proper permissions for VR service files
echo "   Setting VR service permissions..."
chown -R www-data:www-data "$VR_SERVICE_DIR"
chmod -R 755 "$VR_SERVICE_DIR"

# Make scripts executable
find "$VR_SERVICE_DIR" -name "*.sh" -exec chmod +x {} \;

echo ""
echo "🧪 Testing VR service dependencies..."

# Test Node.js modules
echo "   Testing core dependencies..."

# Test Supabase client
node -e "
try {
  const { createClient } = require('@supabase/supabase-js');
  console.log('   ✅ Supabase client: OK');
} catch (e) {
  console.log('   ❌ Supabase client: FAILED');
  process.exit(1);
}
"

# Test Zod validation
node -e "
try {
  const { z } = require('zod');
  console.log('   ✅ Zod validation: OK');
} catch (e) {
  console.log('   ❌ Zod validation: FAILED');
  process.exit(1);
}
"

# Test Express framework
node -e "
try {
  const express = require('express');
  console.log('   ✅ Express framework: OK');
} catch (e) {
  console.log('   ❌ Express framework: FAILED');
  process.exit(1);
}
"

# Test file system utilities
node -e "
try {
  const fs = require('fs-extra');
  const path = require('path');
  console.log('   ✅ File system utilities: OK');
} catch (e) {
  console.log('   ❌ File system utilities: FAILED');
  process.exit(1);
}
"

echo ""
echo "📊 VR Dependencies Installation Summary"
echo "======================================"

# Display installed packages
echo "📦 Installed packages:"
npm list --depth=0 --silent | head -20

echo ""
echo "🔍 System information:"
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"
echo "   PM2: $(pm2 --version)"
echo "   OS: $(uname -a | cut -d' ' -f1-3)"

echo ""
echo "📁 VR service structure:"
echo "   Service directory: $VR_SERVICE_DIR"
echo "   Log directory: /var/log/mvs-vr"
echo "   Scripts directory: /opt/mvs-vr/scripts"

echo ""
if [ -f "package.json" ] && [ -d "node_modules" ]; then
    echo "✅ Task 6.2.4 COMPLETED: VR service dependencies installed successfully!"
    echo ""
    echo "🚀 Ready to proceed with Task 6.2.5: Update NGINX for VR routing"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Deploy VR service files to $VR_SERVICE_DIR"
    echo "   2. Configure NGINX routing for /vendor/vr/"
    echo "   3. Start VR services with PM2"
    echo "   4. Test VR endpoints"
    exit 0
else
    echo "❌ Task 6.2.4 FAILED: VR service dependencies installation incomplete"
    echo "⚠️ Please check the errors above and retry"
    exit 1
fi
