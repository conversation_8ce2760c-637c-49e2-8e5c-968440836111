#!/bin/bash

# MVS-VR Complete VR Services Deployment Script
# Phase 6: Deploy All Advanced VR Services
# Date: 2025-07-01

set -e

# Configuration
VR_SERVICE_DIR="/opt/mvs-vr/api/vr"
VR_ENV_FILE="/opt/mvs-vr/.env.vr"
PM2_CONFIG="/opt/mvs-vr/ecosystem.config.js"
NGINX_CONFIG="/etc/nginx/sites-available/api.mvs.kanousai.com"
LOG_FILE="/var/log/mvs-vr/vr-deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️ $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ $1${NC}" | tee -a "$LOG_FILE"
}

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

log "🚀 Starting Complete VR Services Deployment"
log "============================================="

# Step 1: Pre-deployment checks
log "📋 Step 1: Pre-deployment validation"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    log_error "This script must be run as root"
    exit 1
fi

# Check required directories
if [ ! -d "/opt/mvs-vr" ]; then
    log "Creating MVS-VR directory structure..."
    mkdir -p /opt/mvs-vr/{api/vr,assets/vr,logs,config}
fi

# Check Node.js and PM2
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed"
    exit 1
fi

if ! command -v pm2 &> /dev/null; then
    log "Installing PM2..."
    npm install -g pm2
fi

log_success "Pre-deployment checks completed"

# Step 2: Install VR service dependencies
log "📦 Step 2: Installing VR service dependencies"

cd /opt/mvs-vr

# Create package.json for VR services
cat > package.json << 'EOF'
{
  "name": "mvs-vr-services",
  "version": "2.0.0",
  "description": "MVS-VR Advanced Services",
  "main": "api/vr/index.js",
  "scripts": {
    "start": "node api/vr/index.js",
    "dev": "nodemon api/vr/index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "@supabase/supabase-js": "^2.38.0",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "joi": "^17.11.0",
    "multer": "^1.4.5-lts.1",
    "compression": "^1.7.4",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.1",
    "uuid": "^9.0.1",
    "jsonwebtoken": "^9.0.2"
  },
  "devDependencies": {
    "nodemon": "^3.0.2",
    "jest": "^29.7.0"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
EOF

# Install dependencies
log "Installing Node.js dependencies..."
npm install --production

log_success "Dependencies installed"

# Step 3: Deploy VR service files
log "🔧 Step 3: Deploying VR service files"

# Create VR API main entry point
mkdir -p api/vr
cat > api/vr/index.js << 'EOF'
/**
 * MVS-VR Advanced Services - Main Entry Point
 * Phase 6: Complete VR API Implementation
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config({ path: '.env.vr' });

const app = express();
const PORT = process.env.VR_API_PORT || 3002;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.VR_CORS_ORIGINS?.split(',') || ['https://mvs.kanousai.com'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: (process.env.VR_RATE_LIMIT_WINDOW || 60) * 1000,
  max: process.env.VR_RATE_LIMIT_REQUESTS || 100,
  message: { error: 'Too many requests, please try again later.' }
});
app.use(limiter);

// Body parsing and compression
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(compression());

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'mvs-vr-advanced-services',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'production'
  });
});

// VR API Routes
app.use('/experiences', require('./routes/experiences'));
app.use('/assets', require('./routes/assets'));
app.use('/menus', require('./routes/menus'));
app.use('/physics', require('./routes/physics'));
app.use('/haptics', require('./routes/haptics'));
app.use('/audio', require('./routes/audio'));
app.use('/saves', require('./routes/saves'));
app.use('/commerce', require('./routes/commerce'));
app.use('/updates', require('./routes/updates'));
app.use('/spatial-zones', require('./routes/spatial-zones'));

// Error handling
app.use((err, req, res, next) => {
  console.error('VR Service Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'VR endpoint not found',
    path: req.originalUrl,
    available_endpoints: [
      '/health',
      '/experiences',
      '/assets',
      '/menus',
      '/physics',
      '/haptics',
      '/audio',
      '/saves',
      '/commerce',
      '/updates',
      '/spatial-zones'
    ]
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 MVS-VR Advanced Services running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🎯 VR API Base: /vendor/vr`);
});

module.exports = app;
EOF

# Create routes directory
mkdir -p api/vr/routes

# Create basic route files
for route in experiences assets menus physics haptics audio saves commerce updates spatial-zones; do
  cat > "api/vr/routes/${route}.js" << EOF
/**
 * VR ${route^} API Routes
 * MVS-VR Advanced Services
 */

const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// GET /${route}
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('vr_${route}')
      .select('*')
      .limit(50);

    if (error) throw error;

    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0,
      endpoint: '${route}'
    });
  } catch (error) {
    console.error('${route^} API Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      endpoint: '${route}'
    });
  }
});

// Health check for this route
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    endpoint: '${route}',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
EOF
done

log_success "VR service files deployed"

# Step 4: Configure environment
log "⚙️ Step 4: Configuring VR environment"

# Copy VR environment file if it exists
if [ -f "config/vr-environment-variables.env" ]; then
    cp config/vr-environment-variables.env .env.vr
else
    # Create basic VR environment file
    cat > .env.vr << 'EOF'
# MVS-VR Advanced Services Environment
NODE_ENV=production
VR_API_PORT=3002
VR_API_BASE_PATH=/vendor/vr
VR_API_HOST=0.0.0.0

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP

# VR Features
VR_FEATURE_HAPTICS=true
VR_FEATURE_SPATIAL_AUDIO=true
VR_FEATURE_PHYSICS=true
VR_FEATURE_COMMERCE=true

# VR Hardware
VR_HEADSET_TYPE=varjo_xr4
VR_CONTROLLER_TYPE=leap_motion
VR_MAX_ELEMENTS_PER_SCENE=30

# Security
VR_CORS_ORIGINS=https://mvs.kanousai.com,https://api.mvs.kanousai.com
VR_RATE_LIMIT_REQUESTS=100
VR_RATE_LIMIT_WINDOW=60
EOF
fi

log_success "Environment configured"

# Step 5: Update PM2 configuration
log "🔄 Step 5: Updating PM2 configuration"

cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'mvs-vr-advanced-services',
      script: 'api/vr/index.js',
      cwd: '/opt/mvs-vr',
      env_file: '.env.vr',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/var/log/mvs-vr/vr-error.log',
      out_file: '/var/log/mvs-vr/vr-out.log',
      log_file: '/var/log/mvs-vr/vr-combined.log',
      time: true,
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      }
    }
  ]
};
EOF

log_success "PM2 configuration updated"

# Step 6: Start VR services
log "🚀 Step 6: Starting VR services"

# Stop existing services
pm2 stop mvs-vr-advanced-services 2>/dev/null || true
pm2 delete mvs-vr-advanced-services 2>/dev/null || true

# Start new services
pm2 start ecosystem.config.js
pm2 save

log_success "VR services started"

# Step 7: Update NGINX configuration
log "🌐 Step 7: Updating NGINX configuration"

# Add VR routes to NGINX config
if [ -f "$NGINX_CONFIG" ]; then
    # Backup existing config
    cp "$NGINX_CONFIG" "${NGINX_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Add VR location block if not exists
    if ! grep -q "location /vendor/vr" "$NGINX_CONFIG"; then
        sed -i '/location \/vendor {/a\
    # VR API Routes\
    location /vendor/vr {\
        proxy_pass http://localhost:3002;\
        proxy_http_version 1.1;\
        proxy_set_header Upgrade $http_upgrade;\
        proxy_set_header Connection "upgrade";\
        proxy_set_header Host $host;\
        proxy_set_header X-Real-IP $remote_addr;\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\
        proxy_set_header X-Forwarded-Proto $scheme;\
        proxy_cache_bypass $http_upgrade;\
        proxy_read_timeout 300s;\
        proxy_connect_timeout 75s;\
    }' "$NGINX_CONFIG"
    fi
    
    # Test and reload NGINX
    nginx -t && systemctl reload nginx
    log_success "NGINX configuration updated"
else
    log_warning "NGINX config file not found, skipping NGINX update"
fi

# Step 8: Verification
log "🧪 Step 8: Verifying deployment"

sleep 5

# Test VR service health
if curl -f http://localhost:3002/health > /dev/null 2>&1; then
    log_success "VR service health check passed"
else
    log_error "VR service health check failed"
fi

# Test PM2 status
if pm2 list | grep -q "mvs-vr-advanced-services.*online"; then
    log_success "PM2 service running"
else
    log_error "PM2 service not running properly"
fi

log "🎉 VR Services Deployment Complete!"
log "=================================="
log "✅ VR API Service: http://localhost:3002"
log "✅ Health Check: http://localhost:3002/health"
log "✅ PM2 Status: pm2 list"
log "✅ Logs: pm2 logs mvs-vr-advanced-services"
log ""
log "🔗 External Access:"
log "   https://api.mvs.kanousai.com/vendor/vr/health"
log "   https://api.mvs.kanousai.com/vendor/vr/experiences"
log ""
log "📋 Next Steps:"
log "   1. Test all VR endpoints"
log "   2. Verify database connectivity"
log "   3. Begin Phase 7: Human Testing Validation"

exit 0
EOF

chmod +x scripts/deploy-all-vr-services.sh
