<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendor Registration - MVS-VR Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .registration-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .form-container {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .required {
            color: #e74c3c;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: white;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .next-steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .next-steps h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .next-steps ul {
            list-style: none;
            padding: 0;
        }

        .next-steps li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 25px;
        }

        .next-steps li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .next-steps li:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .form-container {
                padding: 30px 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="header">
            <h1>Join MVS-VR Platform</h1>
            <p>Register your business for immersive VR commerce experiences</p>
        </div>

        <div class="form-container">
            <div id="alerts"></div>

            <form id="registrationForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="company_name">Company Name <span class="required">*</span></label>
                        <input type="text" id="company_name" name="company_name" required>
                    </div>
                    <div class="form-group">
                        <label for="business_type">Business Type <span class="required">*</span></label>
                        <select id="business_type" name="business_type" required>
                            <option value="">Select Business Type</option>
                            <option value="retail">Retail</option>
                            <option value="manufacturing">Manufacturing</option>
                            <option value="technology">Technology</option>
                            <option value="automotive">Automotive</option>
                            <option value="real_estate">Real Estate</option>
                            <option value="fashion">Fashion & Apparel</option>
                            <option value="furniture">Furniture & Home</option>
                            <option value="electronics">Electronics</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="education">Education</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="contact_name">Contact Name <span class="required">*</span></label>
                        <input type="text" id="contact_name" name="contact_name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address <span class="required">*</span></label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="website">Website</label>
                        <input type="url" id="website" name="website" placeholder="https://example.com">
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Business Address</label>
                    <textarea id="address" name="address" placeholder="Enter your business address"></textarea>
                </div>

                <div class="form-group">
                    <label for="description">Business Description</label>
                    <textarea id="description" name="description" placeholder="Tell us about your business and how you plan to use VR commerce"></textarea>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    Register My Business
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing your registration...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = window.location.hostname === 'localhost'
            ? 'http://localhost:3001'
            : 'https://api.mvs.kanousai.com/vendor';

        function showAlert(type, message, details = null) {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            let content = `<strong>${message}</strong>`;
            if (details && Array.isArray(details)) {
                content += '<ul style="margin-top: 10px; margin-bottom: 0;">';
                details.forEach(detail => {
                    content += `<li>${detail}</li>`;
                });
                content += '</ul>';
            }
            
            alert.innerHTML = content;
            alertsContainer.innerHTML = '';
            alertsContainer.appendChild(alert);
            alert.style.display = 'block';
            
            // Scroll to top to show alert
            alertsContainer.scrollIntoView({ behavior: 'smooth' });
        }

        function showNextSteps(steps) {
            const nextStepsHtml = `
                <div class="next-steps">
                    <h3>Next Steps</h3>
                    <ul>
                        ${steps.map(step => `<li>${step}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            const alertsContainer = document.getElementById('alerts');
            const existingNextSteps = alertsContainer.querySelector('.next-steps');
            if (existingNextSteps) {
                existingNextSteps.remove();
            }
            
            alertsContainer.insertAdjacentHTML('beforeend', nextStepsHtml);
        }

        function setLoading(loading) {
            const form = document.getElementById('registrationForm');
            const loadingDiv = document.getElementById('loading');
            const submitBtn = document.getElementById('submitBtn');
            
            if (loading) {
                form.style.display = 'none';
                loadingDiv.style.display = 'block';
                submitBtn.disabled = true;
            } else {
                form.style.display = 'block';
                loadingDiv.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        document.getElementById('registrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            setLoading(true);
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch(`${API_BASE_URL}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('success', 'Registration Successful!', [
                        `Welcome ${result.data.company_name}!`,
                        'Please check your email for verification instructions.'
                    ]);
                    
                    if (result.data.next_steps) {
                        showNextSteps(result.data.next_steps);
                    }
                    
                    // Reset form
                    this.reset();
                } else {
                    showAlert('error', result.error || 'Registration failed', result.details);
                }
            } catch (error) {
                console.error('Registration error:', error);
                showAlert('error', 'Network Error', [
                    'Unable to connect to registration service.',
                    'Please check your internet connection and try again.'
                ]);
            } finally {
                setLoading(false);
            }
        });

        // Form validation enhancements
        document.getElementById('email').addEventListener('blur', function() {
            const email = this.value;
            if (email && !email.includes('@')) {
                this.setCustomValidity('Please enter a valid email address');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('website').addEventListener('blur', function() {
            const website = this.value;
            if (website && !website.startsWith('http')) {
                this.value = 'https://' + website;
            }
        });
    </script>
</body>
</html>
