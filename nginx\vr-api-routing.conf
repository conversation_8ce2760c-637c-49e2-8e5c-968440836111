# NGINX Configuration for VR API Routing
# Task 6.2.5: Update NGINX for VR routing
# Date: 2025-07-01
# 
# This configuration adds VR API routing to the existing NGINX setup
# Add this to the existing api.mvs.kanousai.com.conf file

# VR API endpoints (simplified structure /vendor/vr/)
location /vendor/vr/ {
    # Rate limiting for VR assets (higher burst for large files)
    limit_req zone=vr_assets burst=50 nodelay;
    
    # Proxy to VR API service (port 3002)
    proxy_pass http://127.0.0.1:3002/;
    
    # Standard proxy headers
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # VR-specific headers
    proxy_set_header X-VR-Device $vr_device;
    proxy_set_header X-VR-API "simplified";
    proxy_set_header X-API-Version "2.0";
    proxy_set_header X-Service-Type "vr-api";
    
    # VR-optimized proxy settings
    proxy_buffering off;              # Disable buffering for real-time VR data
    proxy_cache off;                  # Disable caching for dynamic VR content
    proxy_read_timeout 300s;          # 5 minutes for VR asset processing
    proxy_send_timeout 300s;          # 5 minutes for VR asset uploads
    proxy_connect_timeout 30s;        # 30 seconds connection timeout
    proxy_request_buffering off;      # Stream large VR asset uploads
    
    # Large file support for VR assets (150MB max)
    client_max_body_size 150m;
    client_body_timeout 300s;
    client_body_buffer_size 128k;
    client_header_timeout 30s;
    
    # CORS headers for VR applications
    add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-VR-API-Key, X-VR-Device, X-Experience-ID" always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Max-Age "86400" always;
    
    # VR-specific response headers
    add_header X-VR-Service "mvs-vr-api" always;
    add_header X-VR-Version "2.0" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    
    # Handle preflight requests for VR CORS
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "https://mvs.kanousai.com";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-VR-API-Key, X-VR-Device, X-Experience-ID";
        add_header Access-Control-Allow-Credentials "true";
        add_header Access-Control-Max-Age "86400";
        add_header Content-Type "text/plain; charset=utf-8";
        add_header Content-Length 0;
        return 204;
    }
    
    # Error handling for VR services
    proxy_intercept_errors on;
    error_page 502 503 504 /vr_error.html;
    
    # Logging for VR requests
    access_log /var/log/nginx/vr-api-access.log combined;
    error_log /var/log/nginx/vr-api-error.log warn;
}

# VR Asset downloads (optimized for large files)
location /vendor/vr/assets/ {
    # Higher rate limit for asset downloads
    limit_req zone=vr_assets burst=100 nodelay;
    
    # Proxy to VR API service
    proxy_pass http://127.0.0.1:3002/assets/;
    
    # Asset-optimized headers
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Asset-Request "true";
    
    # Asset-specific optimizations
    proxy_buffering on;               # Enable buffering for asset downloads
    proxy_buffer_size 64k;
    proxy_buffers 8 64k;
    proxy_busy_buffers_size 128k;
    proxy_temp_file_write_size 64k;
    
    # Extended timeouts for large assets
    proxy_read_timeout 600s;          # 10 minutes for large asset downloads
    proxy_send_timeout 600s;
    proxy_connect_timeout 30s;
    
    # Asset caching (for static VR assets)
    proxy_cache vr_assets_cache;
    proxy_cache_valid 200 1h;
    proxy_cache_valid 404 1m;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    proxy_cache_lock on;
    proxy_cache_lock_timeout 5s;
    
    # Asset-specific headers
    add_header X-Cache-Status $upstream_cache_status always;
    add_header X-Asset-Service "vr-api" always;
    
    # Large file support
    client_max_body_size 150m;
    client_body_timeout 600s;
}

# VR Health check (lightweight, no rate limiting)
location /vendor/vr/health {
    proxy_pass http://127.0.0.1:3002/health;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Quick health check settings
    proxy_connect_timeout 5s;
    proxy_read_timeout 10s;
    proxy_send_timeout 10s;
    
    # No caching for health checks
    proxy_cache off;
    add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    add_header Pragma "no-cache" always;
    add_header Expires "0" always;
}

# Rate limiting zones (add to http block)
# limit_req_zone $binary_remote_addr zone=vr_assets:10m rate=10r/s;

# VR Asset cache configuration (add to http block)
# proxy_cache_path /var/cache/nginx/vr_assets levels=1:2 keys_zone=vr_assets_cache:10m max_size=1g inactive=60m use_temp_path=off;

# VR Device detection (add to http block if needed)
# map $http_user_agent $vr_device {
#     default "unknown";
#     ~*varjo "varjo_xr4";
#     ~*oculus "oculus";
#     ~*vive "htc_vive";
#     ~*quest "meta_quest";
#     ~*pico "pico";
# }

# VR Error page (create /var/www/html/vr_error.html)
# <!DOCTYPE html>
# <html>
# <head>
#     <title>VR Service Unavailable</title>
# </head>
# <body>
#     <h1>VR Service Temporarily Unavailable</h1>
#     <p>The VR API service is currently unavailable. Please try again later.</p>
#     <p>If this problem persists, please contact support.</p>
# </body>
# </html>
