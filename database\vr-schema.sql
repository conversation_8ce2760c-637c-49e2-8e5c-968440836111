-- MVS-VR Database Schema - VR Services Tables
-- Date: 2025-07-01
-- Purpose: Create all required tables for VR API services

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- VR Experiences Table
-- Stores VR experience configurations and metadata
CREATE TABLE IF NOT EXISTS vr_experiences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    configuration JSONB DEFAULT '{}',
    asset_manifest JSONB DEFAULT '{}',
    environment_settings JSONB DEFAULT '{}',
    performance_settings JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    is_template BOOLEAN DEFAULT false,
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- VR Asset Manifests Table
-- Stores asset metadata for pre-loading and delivery
CREATE TABLE IF NOT EXISTS vr_asset_manifests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    asset_type VARCHAR(50) NOT NULL CHECK (asset_type IN ('model', 'texture', 'audio', 'config', 'material')),
    asset_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    checksum VARCHAR(64),
    priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 10),
    preload BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- VR User Preferences Table
-- Stores user-specific VR settings and preferences
CREATE TABLE IF NOT EXISTS vr_user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, experience_id)
);

-- VR User Saves Table
-- Stores user layout saves and custom configurations
CREATE TABLE IF NOT EXISTS vr_user_saves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    save_name VARCHAR(255) NOT NULL,
    save_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, experience_id, save_name)
);

-- VR Shopping Carts Table
-- Stores VR commerce shopping cart data
CREATE TABLE IF NOT EXISTS vr_shopping_carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
    cart_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, vendor_id)
);

-- VR Haptic Patterns Table
-- Stores haptic feedback patterns for VR experiences
CREATE TABLE IF NOT EXISTS vr_haptic_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    pattern_name VARCHAR(255) NOT NULL,
    pattern_data JSONB DEFAULT '{}',
    device_type VARCHAR(100) DEFAULT 'generic',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, pattern_name)
);

-- VR Audio Zones Table
-- Stores spatial audio zone configurations
CREATE TABLE IF NOT EXISTS vr_audio_zones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    zone_name VARCHAR(255) NOT NULL,
    zone_config JSONB DEFAULT '{}',
    spatial_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, zone_name)
);

-- VR Update Manifests Table
-- Stores background update manifests for VR experiences
CREATE TABLE IF NOT EXISTS vr_update_manifests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID REFERENCES vr_experiences(id) ON DELETE CASCADE,
    version VARCHAR(50) NOT NULL,
    update_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(experience_id, version)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_vr_experiences_vendor_id ON vr_experiences(vendor_id);
CREATE INDEX IF NOT EXISTS idx_vr_experiences_status ON vr_experiences(status);
CREATE INDEX IF NOT EXISTS idx_vr_asset_manifests_experience_id ON vr_asset_manifests(experience_id);
CREATE INDEX IF NOT EXISTS idx_vr_asset_manifests_asset_type ON vr_asset_manifests(asset_type);
CREATE INDEX IF NOT EXISTS idx_vr_user_preferences_user_id ON vr_user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_vr_user_saves_user_id ON vr_user_saves(user_id);
CREATE INDEX IF NOT EXISTS idx_vr_shopping_carts_user_id ON vr_shopping_carts(user_id);
CREATE INDEX IF NOT EXISTS idx_vr_haptic_patterns_experience_id ON vr_haptic_patterns(experience_id);
CREATE INDEX IF NOT EXISTS idx_vr_audio_zones_experience_id ON vr_audio_zones(experience_id);
CREATE INDEX IF NOT EXISTS idx_vr_update_manifests_experience_id ON vr_update_manifests(experience_id);

-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_vr_experiences_updated_at ON vr_experiences;
CREATE TRIGGER update_vr_experiences_updated_at
    BEFORE UPDATE ON vr_experiences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vr_user_preferences_updated_at ON vr_user_preferences;
CREATE TRIGGER update_vr_user_preferences_updated_at
    BEFORE UPDATE ON vr_user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vr_shopping_carts_updated_at ON vr_shopping_carts;
CREATE TRIGGER update_vr_shopping_carts_updated_at
    BEFORE UPDATE ON vr_shopping_carts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE vr_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_asset_manifests ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_user_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_haptic_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_audio_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE vr_update_manifests ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on requirements)
-- VR Experiences: Vendors can only access their own experiences
CREATE POLICY "Vendors can view their own VR experiences" ON vr_experiences
    FOR SELECT USING (vendor_id = auth.uid() OR auth.role() = 'admin');

CREATE POLICY "Vendors can insert their own VR experiences" ON vr_experiences
    FOR INSERT WITH CHECK (vendor_id = auth.uid() OR auth.role() = 'admin');

CREATE POLICY "Vendors can update their own VR experiences" ON vr_experiences
    FOR UPDATE USING (vendor_id = auth.uid() OR auth.role() = 'admin');

-- Asset Manifests: Access through experience ownership
CREATE POLICY "Access VR assets through experience ownership" ON vr_asset_manifests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_asset_manifests.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

-- User data: Users can only access their own data
CREATE POLICY "Users can access their own VR preferences" ON vr_user_preferences
    FOR ALL USING (user_id = auth.uid() OR auth.role() = 'admin');

CREATE POLICY "Users can access their own VR saves" ON vr_user_saves
    FOR ALL USING (user_id = auth.uid() OR auth.role() = 'admin');

CREATE POLICY "Users can access their own VR shopping carts" ON vr_shopping_carts
    FOR ALL USING (user_id = auth.uid() OR auth.role() = 'admin');

-- Haptic patterns, audio zones, update manifests: Access through experience ownership
CREATE POLICY "Access VR haptic patterns through experience ownership" ON vr_haptic_patterns
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_haptic_patterns.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

CREATE POLICY "Access VR audio zones through experience ownership" ON vr_audio_zones
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_audio_zones.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

CREATE POLICY "Access VR update manifests through experience ownership" ON vr_update_manifests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vr_experiences 
            WHERE vr_experiences.id = vr_update_manifests.experience_id 
            AND (vr_experiences.vendor_id = auth.uid() OR auth.role() = 'admin')
        )
    );

-- Insert some sample data for testing (optional)
-- INSERT INTO vr_experiences (vendor_id, name, description, status) VALUES
-- (uuid_generate_v4(), 'Sample VR Experience', 'A test VR experience for development', 'draft');

-- Schema creation complete
SELECT 'VR database schema created successfully!' as result;
