#!/bin/bash

# Deploy VR Health Service
# Task 6.2.6: Deploy VR health service
# Date: 2025-07-01

echo "🏥 VR Health Service Deployment"
echo "Task: 6.2.6 - Deploy VR health service"
echo "=================================="

# Configuration
VR_SERVICE_DIR="/opt/mvs-vr/server/api/vr"
VR_HEALTH_SERVICE="health.js"
SERVICE_NAME="vr-health-service"
VR_API_PORT=3002

echo ""
echo "📁 Preparing VR health service deployment..."

# Create VR service directory
echo "   Creating VR service directory: $VR_SERVICE_DIR"
mkdir -p "$VR_SERVICE_DIR"
chown -R www-data:www-data "$VR_SERVICE_DIR"

# Navigate to VR service directory
cd "$VR_SERVICE_DIR" || exit 1

echo ""
echo "📦 Installing minimal dependencies for health service..."

# Create minimal package.json for health service
cat > package.json << 'EOF'
{
  "name": "vr-health-service",
  "version": "1.0.0",
  "type": "module",
  "main": "health.js",
  "scripts": {
    "start": "node health.js",
    "health": "curl http://localhost:3002/health"
  },
  "dependencies": {
    "@supabase/supabase-js": "^2.39.0",
    "express": "^4.18.2",
    "cors": "^2.8.5"
  }
}
EOF

echo "   Installing health service dependencies..."
npm install --silent

if [ $? -eq 0 ]; then
    echo "   ✅ Health service dependencies installed"
else
    echo "   ❌ Failed to install dependencies"
    exit 1
fi

echo ""
echo "⚙️ Configuring VR health service environment..."

# Create environment file for health service
cat > .env << 'EOF'
# VR Health Service Environment
NODE_ENV=production
VR_API_PORT=3002
VR_API_HOST=0.0.0.0
VR_SERVICE_NAME=vr-health-service

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP
EOF

chmod 640 .env
chown www-data:www-data .env

echo "   ✅ Environment configuration created"

echo ""
echo "🚀 Starting VR health service..."

# Stop existing service if running
if pm2 list | grep -q "$SERVICE_NAME"; then
    echo "   Stopping existing VR health service..."
    pm2 stop "$SERVICE_NAME"
    pm2 delete "$SERVICE_NAME"
fi

# Start VR health service with PM2
echo "   Starting VR health service with PM2..."
pm2 start "$VR_HEALTH_SERVICE" --name "$SERVICE_NAME" --env production

if [ $? -eq 0 ]; then
    echo "   ✅ VR health service started successfully"
else
    echo "   ❌ Failed to start VR health service"
    exit 1
fi

# Save PM2 configuration
pm2 save

echo ""
echo "🧪 Testing VR health service..."

# Wait for service to start
echo "   Waiting for service to initialize..."
sleep 5

# Test local health endpoint
echo "   Testing local health endpoint..."
LOCAL_HEALTH=$(curl -s -w "%{http_code}" http://localhost:$VR_API_PORT/health)
LOCAL_CODE="${LOCAL_HEALTH: -3}"

if [ "$LOCAL_CODE" = "200" ]; then
    echo "   ✅ Local health check passed (HTTP $LOCAL_CODE)"
else
    echo "   ❌ Local health check failed (HTTP $LOCAL_CODE)"
    echo "   Service logs:"
    pm2 logs "$SERVICE_NAME" --lines 10
    exit 1
fi

# Test local status endpoint
echo "   Testing local status endpoint..."
LOCAL_STATUS=$(curl -s -w "%{http_code}" http://localhost:$VR_API_PORT/status)
STATUS_CODE="${LOCAL_STATUS: -3}"

if [ "$STATUS_CODE" = "200" ]; then
    echo "   ✅ Local status check passed (HTTP $STATUS_CODE)"
else
    echo "   ⚠️ Local status check warning (HTTP $STATUS_CODE)"
fi

echo ""
echo "🌐 Testing public VR health endpoint..."

# Test public health endpoint (through NGINX)
echo "   Testing public health endpoint..."
PUBLIC_HEALTH=$(curl -s -w "%{http_code}" https://api.mvs.kanousai.com/vendor/vr/health)
PUBLIC_CODE="${PUBLIC_HEALTH: -3}"

if [ "$PUBLIC_CODE" = "200" ]; then
    echo "   ✅ Public health check passed (HTTP $PUBLIC_CODE)"
elif [ "$PUBLIC_CODE" = "502" ] || [ "$PUBLIC_CODE" = "503" ]; then
    echo "   ⚠️ Public health check shows service unavailable (HTTP $PUBLIC_CODE)"
    echo "   This may indicate NGINX routing needs to be configured"
elif [ "$PUBLIC_CODE" = "404" ]; then
    echo "   ❌ Public health check failed - route not found (HTTP $PUBLIC_CODE)"
    echo "   NGINX VR routing configuration required"
else
    echo "   ⚠️ Public health check status unclear (HTTP $PUBLIC_CODE)"
fi

echo ""
echo "📊 VR Health Service Status"
echo "=========================="

# Display service status
echo "📋 Service Information:"
echo "   Name: $SERVICE_NAME"
echo "   Port: $VR_API_PORT"
echo "   Directory: $VR_SERVICE_DIR"
echo "   Process: $(pm2 list | grep "$SERVICE_NAME" | awk '{print $2}')"

echo ""
echo "🔗 Endpoints:"
echo "   Local Health:  http://localhost:$VR_API_PORT/health"
echo "   Local Status:  http://localhost:$VR_API_PORT/status"
echo "   Public Health: https://api.mvs.kanousai.com/vendor/vr/health"
echo "   Public Status: https://api.mvs.kanousai.com/vendor/vr/status"

echo ""
echo "📝 Service Logs:"
pm2 logs "$SERVICE_NAME" --lines 5

echo ""
if [ "$LOCAL_CODE" = "200" ]; then
    echo "✅ Task 6.2.6 COMPLETED: VR health service deployed and running!"
    echo ""
    echo "🎯 Health service features:"
    echo "   ✅ Simplified endpoint structure (/vendor/vr/)"
    echo "   ✅ Database connectivity testing"
    echo "   ✅ Comprehensive health reporting"
    echo "   ✅ Error handling and logging"
    echo "   ✅ Graceful shutdown support"
    echo ""
    echo "🚀 Ready to proceed with Task 6.2.7: Deploy VR experiences service"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Configure NGINX VR routing (if public endpoint fails)"
    echo "   2. Deploy VR experiences service"
    echo "   3. Deploy VR assets service"
    echo "   4. Test complete VR API functionality"
    exit 0
else
    echo "❌ Task 6.2.6 FAILED: VR health service deployment unsuccessful"
    echo "⚠️ Please check the errors above and retry"
    exit 1
fi
