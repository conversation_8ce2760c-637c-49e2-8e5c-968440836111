{"name": "mvs-vr-validation-system", "version": "1.0.0", "description": "MVS-VR Automated Validation and Monitoring System", "main": "real-time-monitoring-dashboard.js", "scripts": {"start": "node real-time-monitoring-dashboard.js", "health-check": "node automated-health-check-system.js --once", "validate": "node automated-validation-framework.js", "test-endpoints": "node endpoint-testing-suite.js", "test-config": "node configuration-validation-engine.js", "remediate": "node automated-remediation-system.js"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.50.2", "axios": "^1.10.0", "compression": "^1.8.0", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-slow-down": "^2.1.0", "helmet": "^8.1.0", "ws": "^8.14.2"}, "engines": {"node": ">=16.0.0"}, "author": "MVS-VR Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.53.2"}}