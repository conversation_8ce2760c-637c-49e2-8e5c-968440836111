# VR Service Deployment Plan - Simplified Structure
**Date:** 2025-07-01  
**Task:** 6.1.5 - Create VR service deployment plan  
**Status:** ✅ COMPLETE DEPLOYMENT STRATEGY

## 🎯 **DEPLOYMENT STRATEGY OVERVIEW**

Deploy complete VR API system with **simplified `/vendor/vr/` structure** instead of complex `/api/v1/vr/` pattern. Break deployment into **20+ smaller tasks** for better tracking and efficiency.

## 📋 **PHASE-BY-PHASE DEPLOYMENT PLAN**

### **Phase 1: Infrastructure Preparation (Tasks 6.2.1 - 6.2.5)**
**Goal:** Prepare server infrastructure for VR services

#### **Task 6.2.1: Create Supabase VR Database Tables**
- Create 8 VR-specific database tables
- Set up proper relationships and indexes
- Configure row-level security policies
- **Time:** 30 minutes

#### **Task 6.2.2: Set Up VR Asset Storage**
- Create `/opt/mvs-vr/assets/vr/` directory structure
- Set proper permissions and ownership
- Create subdirectories for models, textures, audio, configs
- **Time:** 15 minutes

#### **Task 6.2.3: Configure VR Environment Variables**
- Set VR_API_PORT, VR_API_BASE_PATH, asset paths
- Configure Supabase connection variables
- Set authentication and rate limiting variables
- **Time:** 10 minutes

#### **Task 6.2.4: Install VR Service Dependencies**
- Install Node.js packages (zod, supabase, etc.)
- Verify shared utilities are available
- Check authentication middleware
- **Time:** 15 minutes

#### **Task 6.2.5: Update NGINX for VR Routing**
- Add `/vendor/vr/` location block
- Configure large file support (150MB)
- Set VR-specific timeouts and buffering
- **Time:** 20 minutes

### **Phase 2: Core VR Services Deployment (Tasks 6.3.1 - 6.3.4)**
**Goal:** Deploy essential VR functionality

#### **Task 6.3.1: Deploy VR Health Service**
- Copy and configure VR health endpoint
- Test `/vendor/vr/health` endpoint
- Verify service responds correctly
- **Time:** 10 minutes

#### **Task 6.3.2: Deploy VR Experiences Service**
- Copy `vr/experiences.ts` to deployment location
- Update router paths to `/vendor/vr/experiences`
- Test experience CRUD operations
- **Time:** 20 minutes

#### **Task 6.3.3: Deploy VR Assets Service**
- Copy `vr/assets/` directory to deployment
- Update router paths to `/vendor/vr/assets`
- Test asset download and manifest endpoints
- **Time:** 25 minutes

#### **Task 6.3.4: Deploy VR Menus Service**
- Copy `vr/menus.ts` to deployment location
- Update router paths to `/vendor/vr/menus`
- Test VR menu configuration endpoints
- **Time:** 15 minutes

### **Phase 3: Advanced VR Features (Tasks 6.4.1 - 6.4.4)**
**Goal:** Deploy enhanced VR capabilities

#### **Task 6.4.1: Deploy VR Physics Service**
- Copy `vr/physics.ts` to deployment location
- Update router paths to `/vendor/vr/physics`
- Test physics behavior configuration
- **Time:** 15 minutes

#### **Task 6.4.2: Deploy VR Haptics Service**
- Copy `vr/haptics.ts` to deployment location
- Update router paths to `/vendor/vr/haptics`
- Test haptic pattern configuration
- **Time:** 15 minutes

#### **Task 6.4.3: Deploy VR Audio Service**
- Copy `vr/audio.ts` to deployment location
- Update router paths to `/vendor/vr/audio`
- Test spatial audio zone configuration
- **Time:** 15 minutes

#### **Task 6.4.4: Deploy VR Saves Service**
- Copy `vr/saves.ts` to deployment location
- Update router paths to `/vendor/vr/saves`
- Test user layout save/load functionality
- **Time:** 20 minutes

### **Phase 4: VR Commerce & Updates (Tasks 6.5.1 - 6.5.3)**
**Goal:** Deploy business and maintenance features

#### **Task 6.5.1: Deploy VR Commerce Service**
- Copy `vr/commerce.ts` to deployment location
- Update router paths to `/vendor/vr/commerce`
- Test VR shopping cart and product catalog
- **Time:** 20 minutes

#### **Task 6.5.2: Deploy VR Updates Service**
- Copy `vr/updates.ts` to deployment location
- Update router paths to `/vendor/vr/updates`
- Test background update manifest system
- **Time:** 15 minutes

#### **Task 6.5.3: Deploy VR Spatial Zones Service**
- Copy `vr/spatial-zones.ts` to deployment location
- Update router paths to `/vendor/vr/spatial-zones`
- Test 30-element VR UI system
- **Time:** 20 minutes

### **Phase 5: Integration & Testing (Tasks 6.6.1 - 6.6.5)**
**Goal:** Comprehensive testing and validation

#### **Task 6.6.1: Test All VR Endpoints**
- Test all 11 VR service endpoints
- Verify simplified `/vendor/vr/` structure works
- Check response times and error handling
- **Time:** 30 minutes

#### **Task 6.6.2: Test VR Authentication**
- Test API key authentication for UE plugin
- Verify vendor-scoped access control
- Test rate limiting and security
- **Time:** 20 minutes

#### **Task 6.6.3: Test VR Asset Pipeline**
- Test complete asset upload/download flow
- Verify asset manifests and pre-loading
- Test large file handling (150MB)
- **Time:** 25 minutes

#### **Task 6.6.4: Test VR Database Integration**
- Test all database operations
- Verify data integrity and relationships
- Test concurrent access scenarios
- **Time:** 20 minutes

#### **Task 6.6.5: Create VR Service Health Dashboard**
- Create comprehensive health monitoring
- Test all service endpoints status
- Document performance metrics
- **Time:** 25 minutes

## 🔧 **DEPLOYMENT COMMANDS**

### **File Deployment Commands**
```bash
# Copy VR API files to deployment location
scp -r knowledge/development/implementation-archive/server/api/vr/ root@**************:/opt/mvs-vr/server/api/

# Copy shared utilities
scp -r knowledge/development/implementation-archive/server/shared/ root@**************:/opt/mvs-vr/server/

# Copy middleware
scp -r knowledge/development/implementation-archive/server/middleware/ root@**************:/opt/mvs-vr/server/
```

### **Service Management Commands**
```bash
# Start VR API service
pm2 start /opt/mvs-vr/server/api/vr/index.js --name vr-api-service

# Monitor service
pm2 logs vr-api-service

# Restart service
pm2 restart vr-api-service
```

### **Database Setup Commands**
```sql
-- Run VR database schema creation
psql -h your-supabase-host -U postgres -d your-database -f vr-schema.sql
```

## 📊 **SIMPLIFIED ENDPOINT STRUCTURE**

### **New VR API Structure (Deployed)**
```
/vendor/vr/health          - VR system health check
/vendor/vr/experiences     - VR experience management
/vendor/vr/assets          - VR asset delivery and manifests
/vendor/vr/menus           - VR interactive menu configurations
/vendor/vr/physics         - VR physics behavior configurations
/vendor/vr/haptics         - VR haptic feedback patterns
/vendor/vr/audio           - VR spatial audio zones
/vendor/vr/saves           - VR user layout save data
/vendor/vr/commerce        - VR product catalog and pricing
/vendor/vr/updates         - VR background update manifests
/vendor/vr/spatial-zones   - VR 30-element UI system
```

### **Old Structure (Not Deployed)**
```
/api/v1/vr/experiences/{vendor_id}     # Complex, redundant
/api/v1/vr/assets/{experience_id}      # Complex, redundant
/api/v1/vr/menus/{vendor_id}          # Complex, redundant
```

## ⏱️ **DEPLOYMENT TIMELINE**

### **Total Estimated Time: 6.5 Hours**
- **Phase 1:** Infrastructure (1.5 hours)
- **Phase 2:** Core Services (1.25 hours)
- **Phase 3:** Advanced Features (1.25 hours)
- **Phase 4:** Commerce & Updates (1 hour)
- **Phase 5:** Testing & Validation (1.5 hours)

### **Critical Path Dependencies**
1. **Database tables** must be created before any service deployment
2. **Asset storage** must be configured before asset service
3. **NGINX routing** must be updated before service testing
4. **Authentication middleware** must be available for all services

## 🎯 **SUCCESS CRITERIA**

### **After Complete Deployment**
- [ ] All 11 VR endpoints respond with 200 OK
- [ ] VR asset upload/download works (150MB files)
- [ ] VR experience creation and management functional
- [ ] UE plugin can authenticate and access VR APIs
- [ ] Database operations work correctly
- [ ] Performance targets met (<2s asset downloads)

### **Rollback Plan**
- Keep backup of current system state
- PM2 service rollback commands ready
- Database migration rollback scripts
- NGINX configuration backup

---
**Status:** ✅ COMPLETE DEPLOYMENT PLAN  
**Next Action:** Begin Phase 1 - Infrastructure Preparation  
**Total Tasks:** 20+ smaller deployment tasks  
**Estimated Completion:** 6.5 hours with testing
