# VR Service Dependencies & Requirements Analysis
**Date:** 2025-07-01  
**Task:** 6.1.4 - Identify VR service dependencies and requirements  
**Status:** ✅ COMPLETE ANALYSIS

## 🔍 **VR SERVICE DEPENDENCY ANALYSIS**

Based on analysis of VR API files, here are the complete dependencies and requirements for deploying VR services.

## 📦 **CORE DEPENDENCIES**

### **Runtime Dependencies**
```json
{
  "@supabase/auth-helpers-nextjs": "^0.8.0",
  "@supabase/supabase-js": "^2.0.0",
  "zod": "^3.22.0",
  "next": "^13.0.0",
  "crypto": "built-in",
  "fs": "built-in",
  "path": "built-in"
}
```

### **Shared Utilities Required**
- **Logger Service:** `shared/utils/logger`
- **Error Handler:** `shared/utils/error-handler`
- **Authentication Middleware:** `middleware/auth`
- **API Key Authentication:** `authenticateApiKey` function

### **Database Dependencies**
- **Supabase Client:** Server-side Supabase connection
- **Database Tables:** VR-specific tables (see Database Schema below)

## 🗄️ **DATABASE SCHEMA REQUIREMENTS**

### **Required Supabase Tables**
```sql
-- VR Experiences
CREATE TABLE vr_experiences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vendor_id UUID REFERENCES vendors(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  configuration JSONB DEFAULT '{}',
  asset_manifest JSONB DEFAULT '{}',
  environment_settings JSONB DEFAULT '{}',
  performance_settings JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'draft',
  is_template BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- VR Asset Manifests
CREATE TABLE vr_asset_manifests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id UUID REFERENCES vr_experiences(id),
  asset_type VARCHAR(50) NOT NULL,
  asset_path VARCHAR(500) NOT NULL,
  file_size BIGINT,
  checksum VARCHAR(64),
  priority INTEGER DEFAULT 1,
  preload BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- VR User Preferences
CREATE TABLE vr_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  experience_id UUID REFERENCES vr_experiences(id),
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- VR User Saves
CREATE TABLE vr_user_saves (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  experience_id UUID REFERENCES vr_experiences(id),
  save_name VARCHAR(255),
  save_data JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- VR Shopping Carts
CREATE TABLE vr_shopping_carts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  vendor_id UUID REFERENCES vendors(id),
  cart_data JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- VR Haptic Patterns
CREATE TABLE vr_haptic_patterns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id UUID REFERENCES vr_experiences(id),
  pattern_name VARCHAR(255),
  pattern_data JSONB DEFAULT '{}',
  device_type VARCHAR(100),
  created_at TIMESTAMP DEFAULT NOW()
);

-- VR Audio Zones
CREATE TABLE vr_audio_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id UUID REFERENCES vr_experiences(id),
  zone_name VARCHAR(255),
  zone_config JSONB DEFAULT '{}',
  spatial_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- VR Update Manifests
CREATE TABLE vr_update_manifests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id UUID REFERENCES vr_experiences(id),
  version VARCHAR(50),
  update_data JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 **ENVIRONMENT VARIABLES REQUIRED**

### **Supabase Configuration**
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### **VR Service Configuration**
```bash
VR_API_PORT=3002
VR_API_BASE_PATH=/vendor/vr
VR_ASSET_STORAGE_PATH=/opt/mvs-vr/assets/vr
VR_ASSET_MAX_SIZE=150MB
VR_CACHE_DURATION=3600
VR_PRELOAD_ENABLED=true
```

### **Authentication Configuration**
```bash
VR_API_KEY_HEADER=X-VR-API-Key
VR_SESSION_TIMEOUT=7200
VR_RATE_LIMIT_REQUESTS=100
VR_RATE_LIMIT_WINDOW=60
```

## 📁 **FILE SYSTEM REQUIREMENTS**

### **Asset Storage Structure**
```
/opt/mvs-vr/assets/vr/
├── models/           # 3D model files (.fbx, .obj, .gltf)
├── textures/         # Texture files (.png, .jpg, .dds)
├── audio/            # Audio files (.wav, .ogg, .mp3)
├── configs/          # Configuration files (.json)
├── materials/        # Material definitions (.json)
├── cache/            # Cached processed assets
└── temp/             # Temporary upload processing
```

### **Permissions Required**
```bash
# Asset directory permissions
chmod 755 /opt/mvs-vr/assets/vr/
chown -R www-data:www-data /opt/mvs-vr/assets/vr/

# Service permissions
chmod +x /opt/mvs-vr/server/api/vr/
```

## 🚀 **SERVICE DEPLOYMENT REQUIREMENTS**

### **Node.js Service Configuration**
```javascript
// VR API Service (port 3002)
const VR_SERVICE_CONFIG = {
  port: 3002,
  basePath: '/vendor/vr',
  maxRequestSize: '150mb',
  timeout: 300000, // 5 minutes for large asset operations
  cors: {
    origin: ['https://mvs.kanousai.com', 'https://api.mvs.kanousai.com'],
    credentials: true
  }
};
```

### **PM2 Configuration**
```json
{
  "name": "vr-api-service",
  "script": "server/api/vr/index.js",
  "instances": 2,
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "VR_API_PORT": 3002,
    "VR_API_BASE_PATH": "/vendor/vr"
  },
  "error_file": "/var/log/pm2/vr-api-error.log",
  "out_file": "/var/log/pm2/vr-api-out.log",
  "log_file": "/var/log/pm2/vr-api.log"
}
```

## 🔗 **NGINX CONFIGURATION REQUIRED**

### **VR API Routing**
```nginx
# VR API endpoints (simplified structure)
location /vendor/vr/ {
    limit_req zone=vr_assets burst=50 nodelay;
    
    proxy_pass http://127.0.0.1:3002/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-VR-Device $vr_device;
    
    # VR-specific optimizations
    proxy_buffering off;
    proxy_cache off;
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
    proxy_request_buffering off;
    
    # Large file support for VR assets
    client_max_body_size 150m;
    client_body_timeout 300s;
}
```

## 🔒 **SECURITY REQUIREMENTS**

### **API Key Authentication**
- UE Plugin API keys for secure VR access
- Vendor-scoped API key permissions
- Rate limiting for VR asset downloads

### **Asset Security**
- Checksum validation for asset integrity
- Secure asset storage with access controls
- Encrypted asset transmission

## 📊 **PERFORMANCE REQUIREMENTS**

### **Hardware Requirements**
- **CPU:** Multi-core for asset processing
- **RAM:** 8GB+ for asset caching
- **Storage:** SSD for fast asset delivery
- **Network:** High bandwidth for asset downloads

### **Performance Targets**
- **Asset Download:** <2s for 10MB assets
- **API Response:** <200ms for metadata
- **Concurrent Users:** 50+ simultaneous VR sessions
- **Asset Cache Hit Rate:** >80%

## ✅ **DEPLOYMENT CHECKLIST**

### **Pre-Deployment**
- [ ] Supabase database tables created
- [ ] Environment variables configured
- [ ] Asset storage directories created
- [ ] NGINX configuration updated
- [ ] PM2 configuration prepared

### **Dependencies Installation**
- [ ] Node.js dependencies installed
- [ ] Shared utilities available
- [ ] Authentication middleware deployed
- [ ] Logger service configured

### **Post-Deployment Testing**
- [ ] VR health endpoint responds
- [ ] Asset download functionality works
- [ ] Database connections established
- [ ] Authentication working
- [ ] Performance targets met

---
**Status:** ✅ COMPLETE DEPENDENCY ANALYSIS  
**Next Task:** 6.1.5 - Create VR service deployment plan  
**Critical Dependencies:** Supabase tables, asset storage, authentication middleware
