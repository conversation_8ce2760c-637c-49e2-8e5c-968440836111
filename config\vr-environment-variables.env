# MVS-VR Environment Variables Configuration
# Task 6.2.3: Configure VR environment variables
# Date: 2025-07-01

# ============================================================================
# VR SERVICE CONFIGURATION
# ============================================================================

# VR API Service Configuration
VR_API_PORT=3002
VR_API_BASE_PATH=/vendor/vr
VR_API_HOST=0.0.0.0
VR_API_TIMEOUT=300000

# VR Asset Storage Configuration
VR_ASSET_STORAGE_PATH=/opt/mvs-vr/assets/vr
VR_ASSET_MAX_SIZE=150MB
VR_ASSET_CACHE_DURATION=3600
VR_ASSET_PRELOAD_ENABLED=true
VR_ASSET_COMPRESSION_ENABLED=true
VR_ASSET_BACKUP_ENABLED=true

# VR Asset Type Directories
VR_ASSET_MODELS_PATH=/opt/mvs-vr/assets/vr/models
VR_ASSET_TEXTURES_PATH=/opt/mvs-vr/assets/vr/textures
VR_ASSET_AUDIO_PATH=/opt/mvs-vr/assets/vr/audio
VR_ASSET_CONFIGS_PATH=/opt/mvs-vr/assets/vr/configs
VR_ASSET_MATERIALS_PATH=/opt/mvs-vr/assets/vr/materials
VR_ASSET_CACHE_PATH=/opt/mvs-vr/assets/vr/cache
VR_ASSET_TEMP_PATH=/opt/mvs-vr/assets/vr/temp

# ============================================================================
# SUPABASE DATABASE CONFIGURATION
# ============================================================================

# Supabase Connection (provided credentials)
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP

# Legacy Supabase Variables (for compatibility)
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP

# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# ============================================================================
# VR AUTHENTICATION CONFIGURATION
# ============================================================================

# VR API Key Authentication
VR_API_KEY_HEADER=X-VR-API-Key
VR_SESSION_TIMEOUT=7200
VR_API_KEY_LENGTH=32

# Rate Limiting for VR Services
VR_RATE_LIMIT_REQUESTS=100
VR_RATE_LIMIT_WINDOW=60
VR_RATE_LIMIT_BURST=50

# ============================================================================
# VR HARDWARE CONFIGURATION
# ============================================================================

# Varjo XR4 Headset Configuration
VR_HEADSET_TYPE=varjo_xr4
VR_HEADSET_RESOLUTION=2880x1700
VR_HEADSET_REFRESH_RATE=90

# Leap Motion Controller Configuration
VR_CONTROLLER_TYPE=leap_motion
VR_CONTROLLER_TRACKING_MODE=desktop
VR_HAND_TRACKING_ENABLED=true

# Haptic Feedback Configuration
VR_HAPTICS_ENABLED=true
VR_HAPTICS_DEVICE_TYPE=generic
VR_HAPTICS_INTENSITY=0.8

# ============================================================================
# VR PERFORMANCE CONFIGURATION
# ============================================================================

# VR Rendering Settings
VR_RENDER_SCALE=1.0
VR_ANTI_ALIASING=4x
VR_TEXTURE_QUALITY=high
VR_SHADOW_QUALITY=medium

# VR Asset Loading
VR_ASSET_PRELOAD_COUNT=30
VR_ASSET_BACKGROUND_LOADING=true
VR_ASSET_STREAMING_ENABLED=false

# VR Memory Management
VR_MEMORY_LIMIT=4GB
VR_CACHE_SIZE=1GB
VR_GARBAGE_COLLECTION_INTERVAL=300

# ============================================================================
# VR EXPERIENCE CONFIGURATION
# ============================================================================

# VR Experience Limits
VR_MAX_ELEMENTS_PER_SCENE=30
VR_MAX_CONCURRENT_USERS=50
VR_MAX_EXPERIENCE_SIZE=500MB

# VR Spatial Configuration
VR_SPATIAL_ZONES_ENABLED=true
VR_SPATIAL_AUDIO_ENABLED=true
VR_PHYSICS_ENABLED=true

# VR Commerce Configuration
VR_COMMERCE_ENABLED=true
VR_SHOPPING_CART_TIMEOUT=1800
VR_PAYMENT_PROCESSING_ENABLED=false

# ============================================================================
# VR LOGGING AND MONITORING
# ============================================================================

# VR Service Logging
VR_LOG_LEVEL=info
VR_LOG_FILE=/var/log/mvs-vr/vr-service.log
VR_LOG_MAX_SIZE=100MB
VR_LOG_MAX_FILES=5

# VR Performance Monitoring
VR_MONITORING_ENABLED=true
VR_METRICS_COLLECTION_INTERVAL=60
VR_PERFORMANCE_ALERTS_ENABLED=true

# VR Error Tracking
VR_ERROR_TRACKING_ENABLED=true
VR_ERROR_REPORTING_ENDPOINT=https://api.mvs.kanousai.com/errors

# ============================================================================
# VR DEVELOPMENT CONFIGURATION
# ============================================================================

# Development Mode Settings
VR_DEVELOPMENT_MODE=false
VR_DEBUG_ENABLED=false
VR_VERBOSE_LOGGING=false

# VR Testing Configuration
VR_TEST_MODE=false
VR_MOCK_HARDWARE=false
VR_SIMULATION_ENABLED=false

# ============================================================================
# VR SECURITY CONFIGURATION
# ============================================================================

# VR API Security
VR_CORS_ORIGINS=https://mvs.kanousai.com,https://api.mvs.kanousai.com
VR_CSRF_PROTECTION_ENABLED=true
VR_REQUEST_VALIDATION_ENABLED=true

# VR Asset Security
VR_ASSET_ENCRYPTION_ENABLED=false
VR_ASSET_CHECKSUM_VALIDATION=true
VR_SECURE_ASSET_DELIVERY=true

# ============================================================================
# VR INTEGRATION CONFIGURATION
# ============================================================================

# UE Plugin Integration
UE_PLUGIN_API_ENDPOINT=https://api.mvs.kanousai.com/vendor/vr
UE_PLUGIN_AUTH_METHOD=api_key
UE_PLUGIN_TIMEOUT=30000

# Vendor Portal Integration
VENDOR_PORTAL_VR_ENABLED=true
VENDOR_PORTAL_VR_ENDPOINT=/vendor/vr
VENDOR_PORTAL_VR_PERMISSIONS=create,read,update,delete

# Admin Portal Integration
ADMIN_PORTAL_VR_ENABLED=true
ADMIN_PORTAL_VR_MONITORING=true
ADMIN_PORTAL_VR_ANALYTICS=true

# ============================================================================
# VR BACKUP AND RECOVERY
# ============================================================================

# VR Data Backup
VR_BACKUP_ENABLED=true
VR_BACKUP_INTERVAL=daily
VR_BACKUP_RETENTION_DAYS=30
VR_BACKUP_LOCATION=/opt/mvs-vr/assets/vr/backups

# VR Recovery Configuration
VR_RECOVERY_ENABLED=true
VR_AUTO_RECOVERY=false
VR_RECOVERY_TIMEOUT=300

# ============================================================================
# VR FEATURE FLAGS
# ============================================================================

# VR Feature Toggles
VR_FEATURE_HAPTICS=true
VR_FEATURE_SPATIAL_AUDIO=true
VR_FEATURE_PHYSICS=true
VR_FEATURE_COMMERCE=true
VR_FEATURE_USER_SAVES=true
VR_FEATURE_BACKGROUND_UPDATES=true
VR_FEATURE_ANALYTICS=true
VR_FEATURE_COLLABORATION=false

# Experimental Features
VR_EXPERIMENTAL_AI_ASSISTANCE=false
VR_EXPERIMENTAL_VOICE_COMMANDS=false
VR_EXPERIMENTAL_GESTURE_RECOGNITION=false
