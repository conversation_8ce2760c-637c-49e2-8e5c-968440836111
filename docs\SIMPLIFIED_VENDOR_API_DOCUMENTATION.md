# MVS-VR Simplified Vendor API Documentation
**Version:** 2.0  
**Date:** 2025-07-01  
**Status:** ✅ READY FOR DEPLOYMENT

## Overview

The MVS-VR Vendor API has been simplified to remove redundant `/api/` segments while maintaining full backward compatibility. This creates a cleaner, more intuitive API structure for vendor operations.

## API Endpoint Structure

### ✅ New Simplified Endpoints (Recommended)

#### Vendor Registration
```
POST https://api.mvs.kanousai.com/vendor/register
Content-Type: application/json

{
  "company_name": "Example Company",
  "email": "<EMAIL>",
  "contact_name": "<PERSON>",
  "business_type": "retail",
  "website": "https://example.com",
  "description": "Company description"
}
```

#### Email Verification
```
GET https://api.mvs.kanousai.com/vendor/verify?token=verification_token&email=<EMAIL>
```

#### Health Check
```
GET https://api.mvs.kanousai.com/vendor/health
```

### ⚠️ Deprecated Endpoints (Backward Compatibility)

These endpoints still work but show deprecation warnings:

```
POST https://api.mvs.kanousai.com/vendor/api/register  # Use /vendor/register instead
GET  https://api.mvs.kanousai.com/vendor/api/verify   # Use /vendor/verify instead  
GET  https://api.mvs.kanousai.com/vendor/api/health   # Use /vendor/health instead
```

## Response Format

### Successful Registration Response
```json
{
  "success": true,
  "message": "Vendor registration successful",
  "data": {
    "vendor_id": "uuid",
    "company_name": "Example Company",
    "email": "<EMAIL>",
    "status": "pending_verification",
    "verification_token": "token",
    "next_steps": [
      "Check your email for verification instructions",
      "Click the verification link to activate your account"
    ]
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "details": [
    "Company name is required",
    "Email format is invalid"
  ]
}
```

### Deprecation Warning Response
```json
{
  "success": true,
  "message": "Request successful",
  "deprecated": true,
  "warning": "This endpoint is deprecated. Please use /vendor/register instead of /vendor/api/register",
  "new_endpoint": "/vendor/register",
  "data": { ... }
}
```

## Authentication & Security

### Rate Limiting
- **Registration:** 5 requests per 15 minutes per IP
- **Verification:** 10 requests per hour per IP
- **Health Check:** 60 requests per minute per IP

### Required Headers
```
Content-Type: application/json
User-Agent: Your-Application/1.0
```

### Optional Headers
```
X-Vendor-API: simplified
X-API-Version: 2.0
```

## Migration Guide

### For Existing Clients

#### Before (Old Structure)
```javascript
const API_BASE = 'https://api.mvs.kanousai.com/vendor/api';

// Registration
fetch(`${API_BASE}/register`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(vendorData)
});
```

#### After (Simplified Structure)
```javascript
const API_BASE = 'https://api.mvs.kanousai.com/vendor';

// Registration  
fetch(`${API_BASE}/register`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(vendorData)
});
```

### Migration Timeline
1. **Phase 1:** Deploy simplified endpoints (backward compatibility maintained)
2. **Phase 2:** Update client applications to use simplified endpoints
3. **Phase 3:** Monitor usage of deprecated endpoints
4. **Phase 4:** Remove deprecated endpoints (future release)

## Future Enhancements

### VR Endpoint Consolidation (Planned)
The VR API endpoints will also be simplified and consolidated under the vendor namespace:

```
Current: /api/v1/vr/experiences/{vendor_id}
Future:  /vendor/vr/experiences

Current: /api/v1/vr/assets/{experience_id}  
Future:  /vendor/vr/assets

Current: /api/v1/vr/menus/{vendor_id}
Future:  /vendor/vr/menus
```

## Testing

### Health Check Test
```bash
curl https://api.mvs.kanousai.com/vendor/health
```

### Registration Test
```bash
curl -X POST \
  https://api.mvs.kanousai.com/vendor/register \
  -H 'Content-Type: application/json' \
  -d '{
    "company_name": "Test Company",
    "email": "<EMAIL>",
    "contact_name": "Test User",
    "business_type": "retail"
  }'
```

### Deprecation Warning Test
```bash
curl -X POST \
  https://api.mvs.kanousai.com/vendor/api/register \
  -H 'Content-Type: application/json' \
  -d '{
    "company_name": "Test Company",
    "email": "<EMAIL>"
  }'
```

## Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `VENDOR_EXISTS` | Vendor already registered | 409 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `VERIFICATION_ERROR` | Email verification failed | 400 |
| `INTERNAL_ERROR` | Server error | 500 |

## Support

### Documentation
- API Documentation: `/docs/SIMPLIFIED_VENDOR_API_DOCUMENTATION.md`
- Migration Guide: See "Migration Guide" section above
- Test Results: `/docs/comprehensive-endpoint-test-report.md`

### Contact
- Technical Issues: Check server logs and endpoint health
- API Questions: Refer to this documentation
- Feature Requests: Submit via project management system

---

**Documentation Version:** 2.0  
**Last Updated:** 2025-07-01  
**API Status:** ✅ Ready for deployment  
**Backward Compatibility:** ✅ Fully maintained
