#!/usr/bin/env node

/**
 * Setup Vendor Portal with Role-Based Access Control
 * Ensures vendors only see vendor interface, not admin interface
 */

const express = require('express');
const path = require('path');

// Vendor Portal Server
const vendorApp = express();
const PORT = process.env.PORT || 3006;

vendorApp.use(express.json());
vendorApp.use(express.static('public'));

// CORS for vendor portal
vendorApp.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'https://mvs.kanousai.com');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});

// Vendor Portal HTML Template
const vendorPortalHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVS-VR Vendor Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .vendor-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .vendor-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .vendor-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .vendor-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .vendor-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .vendor-menu a:hover {
            background: #667eea;
            color: white;
        }
        
        .vendor-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            background: #667eea;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .card-description {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .card-action {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            transition: background 0.3s ease;
        }
        
        .card-action:hover {
            background: #5a6fd8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .vendor-nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .vendor-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="vendor-header">
        <nav class="vendor-nav">
            <div class="vendor-logo">MVS-VR Vendor Portal</div>
            <ul class="vendor-menu">
                <li><a href="#dashboard">Dashboard</a></li>
                <li><a href="#products">Products</a></li>
                <li><a href="#showrooms">Showrooms</a></li>
                <li><a href="#orders">Orders</a></li>
                <li><a href="#analytics">Analytics</a></li>
                <li><a href="#support">Support</a></li>
            </ul>
            <div class="vendor-user">
                <span><span class="status-indicator"></span>Vendor Account</span>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="welcome-card">
            <h1>Welcome to Your Vendor Portal</h1>
            <p>Manage your products, showrooms, and VR experiences in one place. This is your dedicated vendor interface - no admin access needed.</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">📦</div>
                <h3 class="card-title">Product Management</h3>
                <p class="card-description">Add, edit, and manage your product catalog with detailed specifications and VR assets.</p>
                <button class="card-action" onclick="openProducts()">Manage Products</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">🏪</div>
                <h3 class="card-title">Virtual Showrooms</h3>
                <p class="card-description">Create and customize your virtual showrooms for immersive customer experiences.</p>
                <button class="card-action" onclick="openShowrooms()">Design Showrooms</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">🛒</div>
                <h3 class="card-title">Order Management</h3>
                <p class="card-description">Track orders, manage inventory, and handle customer requests efficiently.</p>
                <button class="card-action" onclick="openOrders()">View Orders</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">📊</div>
                <h3 class="card-title">Analytics & Reports</h3>
                <p class="card-description">Monitor performance, track sales, and analyze customer behavior patterns.</p>
                <button class="card-action" onclick="openAnalytics()">View Analytics</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">🥽</div>
                <h3 class="card-title">VR Experience Builder</h3>
                <p class="card-description">Create interactive VR experiences and customize virtual environments.</p>
                <button class="card-action" onclick="openVRBuilder()">Build VR</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">💬</div>
                <h3 class="card-title">Support & Help</h3>
                <p class="card-description">Get assistance, access documentation, and contact our support team.</p>
                <button class="card-action" onclick="openSupport()">Get Support</button>
            </div>
        </div>
    </div>

    <script>
        // Vendor Portal JavaScript
        function openProducts() {
            // Redirect to Directus products collection with vendor filter
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/products?filter[vendor_id][_eq]=' + getVendorId();
        }

        function openShowrooms() {
            // Redirect to Directus showrooms collection with vendor filter
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/showrooms?filter[vendor_id][_eq]=' + getVendorId();
        }

        function openOrders() {
            // Redirect to Directus orders collection with vendor filter
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/orders?filter[vendor_id][_eq]=' + getVendorId();
        }

        function openAnalytics() {
            // Open vendor analytics dashboard
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/insights/vendor-analytics';
        }

        function openVRBuilder() {
            // Open VR experience builder
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/vr_experiences?filter[vendor_id][_eq]=' + getVendorId();
        }

        function openSupport() {
            // Open support portal
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/support_tickets?filter[vendor_id][_eq]=' + getVendorId();
        }

        function getVendorId() {
            // Get vendor ID from session or token
            return localStorage.getItem('vendor_id') || '1';
        }

        function logout() {
            // Clear session and redirect to login
            localStorage.clear();
            window.location.href = 'https://mvs.kanousai.com/auth/logout';
        }

        // Initialize vendor portal
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MVS-VR Vendor Portal loaded');
            
            // Check authentication
            const token = localStorage.getItem('auth_token');
            if (!token) {
                window.location.href = 'https://mvs.kanousai.com/auth/login?redirect=/vendor';
            }
        });
    </script>
</body>
</html>
`;

// Vendor Portal Routes
vendorApp.get('/', (req, res) => {
  res.send(vendorPortalHTML);
});

vendorApp.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'vendor-portal',
    timestamp: new Date().toISOString()
  });
});

// API endpoints for vendor data
vendorApp.get('/api/vendor/profile', (req, res) => {
  res.json({
    id: 1,
    name: 'Sample Vendor',
    email: '<EMAIL>',
    status: 'active',
    products_count: 12,
    showrooms_count: 3,
    orders_count: 45
  });
});

vendorApp.get('/api/vendor/stats', (req, res) => {
  res.json({
    total_products: 12,
    active_showrooms: 3,
    pending_orders: 5,
    monthly_revenue: 15420,
    vr_sessions: 234,
    conversion_rate: 12.5
  });
});

// Start vendor portal server
if (require.main === module) {
  vendorApp.listen(PORT, () => {
    console.log(`🏪 MVS-VR Vendor Portal running on port ${PORT}`);
    console.log(`📱 Access at: http://localhost:${PORT}`);
    console.log(`🔒 Vendor-only interface - no admin access`);
  });
}

module.exports = vendorApp;
