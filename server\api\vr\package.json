{"name": "mvs-vr-api-services", "version": "2.0.0", "description": "MVS-VR API Services - Simplified VR endpoints for vendor-specific VR experiences", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "test": "node --test test/*.test.js", "build": "echo 'No build step required for Node.js service'", "lint": "eslint *.js **/*.js", "validate": "node scripts/validate-vr-services.js", "health": "curl http://localhost:3002/vendor/vr/health", "deploy": "pm2 start ecosystem.config.js", "logs": "pm2 logs vr-api-service", "restart": "pm2 restart vr-api-service", "stop": "pm2 stop vr-api-service"}, "keywords": ["vr", "virtual-reality", "api", "mvs-vr", "vendor", "experiences", "assets", "haptics", "spatial-audio", "varjo-xr4", "leap-motion"], "author": "MVS-VR Development Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.50.2", "archiver": "^6.0.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "path": "^0.12.7", "sharp": "^0.33.1", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "repository": {"type": "git", "url": "git+https://github.com/mvs-vr/mvs-vr-v2.git"}, "bugs": {"url": "https://github.com/mvs-vr/mvs-vr-v2/issues"}, "homepage": "https://mvs.kanousai.com", "config": {"port": 3002, "host": "0.0.0.0", "env": "production"}, "pm2": {"name": "vr-api-service", "script": "index.js", "instances": 2, "exec_mode": "cluster", "watch": false, "max_memory_restart": "1G", "env": {"NODE_ENV": "production", "VR_API_PORT": 3002, "VR_API_BASE_PATH": "/vendor/vr"}, "env_development": {"NODE_ENV": "development", "VR_API_PORT": 3002, "VR_DEBUG_ENABLED": true}, "log_file": "/var/log/pm2/vr-api.log", "error_file": "/var/log/pm2/vr-api-error.log", "out_file": "/var/log/pm2/vr-api-out.log", "merge_logs": true, "time": true}, "eslintConfig": {"extends": ["standard"], "env": {"node": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}}, "jest": {"testEnvironment": "node", "testMatch": ["**/test/**/*.test.js"], "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/test/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}