# Check what vendor service is actually running on DO
Write-Host "🔍 Checking DigitalOcean vendor service..." -ForegroundColor Blue

$SERVER_IP = "**************"
$SERVER_USER = "root"

Write-Host "`n📋 Step 1: Check running processes..." -ForegroundColor Yellow
ssh ${SERVER_USER}@${SERVER_IP} "pm2 list"

Write-Host "`n📁 Step 2: Check vendor service files..." -ForegroundColor Yellow
ssh ${SERVER_USER}@${SERVER_IP} "find /opt -name '*vendor*' -type f 2>/dev/null | head -10"

Write-Host "`n🔍 Step 3: Check for vendor API files..." -ForegroundColor Yellow
ssh ${SERVER_USER}@${SERVER_IP} "ls -la /opt/mvs-vr/app/api/ 2>/dev/null || echo 'Directory not found'"

Write-Host "`n📝 Step 4: Check PM2 logs for vendor service..." -ForegroundColor Yellow
ssh ${SERVER_USER}@${SERVER_IP} "pm2 logs --lines 5 2>/dev/null || echo 'No PM2 logs found'"

Write-Host "`n✅ Investigation complete." -ForegroundColor Green
