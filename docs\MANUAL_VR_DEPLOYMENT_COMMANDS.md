# Manual VR Services Deployment Commands
**Date:** 2025-07-01  
**Purpose:** Deploy VR services without SSH key access  

## 🚀 Step-by-Step Manual Deployment

### Step 1: Access Your Server
Use whatever method you normally use to access your DigitalOcean server (web console, password SSH, etc.)

### Step 2: Create VR Service Directory Structure
```bash
# Create directories
mkdir -p /opt/mvs-vr/{api/vr/routes,logs,config}
cd /opt/mvs-vr
```

### Step 3: Create Package.json
```bash
cat > package.json << 'EOF'
{
  "name": "mvs-vr-services",
  "version": "2.0.0",
  "description": "MVS-VR Advanced Services",
  "main": "api/vr/index.js",
  "scripts": {
    "start": "node api/vr/index.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "@supabase/supabase-js": "^2.38.0",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "compression": "^1.7.4",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.1"
  }
}
EOF
```

### Step 4: Install Dependencies
```bash
npm install --production
```

### Step 5: Create VR Environment File
```bash
cat > .env.vr << 'EOF'
NODE_ENV=production
VR_API_PORT=3002
VR_API_BASE_PATH=/vendor/vr
VR_API_HOST=0.0.0.0

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP

# VR Features
VR_FEATURE_HAPTICS=true
VR_FEATURE_SPATIAL_AUDIO=true
VR_FEATURE_PHYSICS=true
VR_FEATURE_COMMERCE=true

# VR Hardware
VR_HEADSET_TYPE=varjo_xr4
VR_CONTROLLER_TYPE=leap_motion
VR_MAX_ELEMENTS_PER_SCENE=30

# Security
VR_CORS_ORIGINS=https://mvs.kanousai.com,https://api.mvs.kanousai.com
VR_RATE_LIMIT_REQUESTS=100
VR_RATE_LIMIT_WINDOW=60
EOF
```

### Step 6: Create Main VR Service File
```bash
cat > api/vr/index.js << 'EOF'
/**
 * MVS-VR Advanced Services - Main Entry Point
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config({ path: '.env.vr' });

const app = express();
const PORT = process.env.VR_API_PORT || 3002;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.VR_CORS_ORIGINS?.split(',') || ['https://mvs.kanousai.com'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: (process.env.VR_RATE_LIMIT_WINDOW || 60) * 1000,
  max: process.env.VR_RATE_LIMIT_REQUESTS || 100,
  message: { error: 'Too many requests, please try again later.' }
});
app.use(limiter);

// Body parsing and compression
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(compression());

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'mvs-vr-advanced-services',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'production'
  });
});

// VR API Routes
app.use('/experiences', require('./routes/experiences'));
app.use('/assets', require('./routes/assets'));
app.use('/menus', require('./routes/menus'));
app.use('/physics', require('./routes/physics'));
app.use('/haptics', require('./routes/haptics'));
app.use('/audio', require('./routes/audio'));
app.use('/saves', require('./routes/saves'));
app.use('/commerce', require('./routes/commerce'));
app.use('/updates', require('./routes/updates'));
app.use('/spatial-zones', require('./routes/spatial-zones'));

// Error handling
app.use((err, req, res, next) => {
  console.error('VR Service Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'VR endpoint not found',
    path: req.originalUrl,
    available_endpoints: [
      '/health',
      '/experiences',
      '/assets',
      '/menus',
      '/physics',
      '/haptics',
      '/audio',
      '/saves',
      '/commerce',
      '/updates',
      '/spatial-zones'
    ]
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 MVS-VR Advanced Services running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🎯 VR API Base: /vendor/vr`);
});

module.exports = app;
EOF
```

### Step 7: Create VR Route Files
Run these commands to create each route file:

```bash
# Create experiences route
cat > api/vr/routes/experiences.js << 'EOF'
const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('vr_experiences')
      .select('*')
      .limit(50);

    if (error) throw error;

    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0,
      endpoint: 'experiences'
    });
  } catch (error) {
    console.error('Experiences API Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      endpoint: 'experiences'
    });
  }
});

router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    endpoint: 'experiences',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
EOF

# Create assets route
cat > api/vr/routes/assets.js << 'EOF'
const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('vr_asset_manifests')
      .select('*')
      .limit(50);

    if (error) throw error;

    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0,
      endpoint: 'assets'
    });
  } catch (error) {
    console.error('Assets API Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      endpoint: 'assets'
    });
  }
});

router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    endpoint: 'assets',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
EOF
```

### Step 8: Create Remaining Route Files (Quick Method)
```bash
# Create all other route files quickly
for route in menus physics haptics audio saves commerce updates spatial-zones; do
  cat > "api/vr/routes/${route}.js" << EOF
const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

router.get('/', async (req, res) => {
  try {
    res.json({
      success: true,
      data: [],
      count: 0,
      endpoint: '${route}',
      message: 'VR ${route} endpoint ready'
    });
  } catch (error) {
    console.error('${route^} API Error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      endpoint: '${route}'
    });
  }
});

router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    endpoint: '${route}',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
EOF
done
```

### Step 9: Create PM2 Configuration
```bash
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'mvs-vr-advanced-services',
      script: 'api/vr/index.js',
      cwd: '/opt/mvs-vr',
      env_file: '.env.vr',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/var/log/mvs-vr/vr-error.log',
      out_file: '/var/log/mvs-vr/vr-out.log',
      log_file: '/var/log/mvs-vr/vr-combined.log',
      time: true,
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      }
    }
  ]
};
EOF
```

### Step 10: Start VR Services
```bash
# Create log directory
mkdir -p /var/log/mvs-vr

# Stop any existing VR services
pm2 stop mvs-vr-advanced-services 2>/dev/null || true
pm2 delete mvs-vr-advanced-services 2>/dev/null || true

# Start new VR services
pm2 start ecosystem.config.js
pm2 save

# Check status
pm2 list
```

### Step 11: Test VR Services
```bash
# Test local health check
curl http://localhost:3002/health

# Test VR endpoints
curl http://localhost:3002/experiences
curl http://localhost:3002/assets
```

### Step 12: Update NGINX (if needed)
```bash
# Check if VR routing exists in NGINX config
grep -n "location /vendor/vr" /etc/nginx/sites-available/api.mvs.kanousai.com

# If not found, add VR routing
nano /etc/nginx/sites-available/api.mvs.kanousai.com
```

Add this location block inside the server block:
```nginx
# VR API Routes
location /vendor/vr {
    proxy_pass http://localhost:3002;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
}
```

Then reload NGINX:
```bash
nginx -t && systemctl reload nginx
```

### Step 13: Final Verification
```bash
# Test external access
curl https://api.mvs.kanousai.com/vendor/vr/health
curl https://api.mvs.kanousai.com/vendor/vr/experiences
```

## 🎯 Expected Results
- ✅ PM2 shows "mvs-vr-advanced-services" running
- ✅ Local health check returns JSON response
- ✅ External VR endpoints accessible via HTTPS
- ✅ All 10 VR endpoints responding

Let me know when you've completed these steps and I'll help you verify the deployment and move to Phase 7!
