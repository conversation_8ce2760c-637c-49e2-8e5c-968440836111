/**
 * VR Health Service
 * Task 6.2.6: Deploy VR health service
 * Date: 2025-07-01
 * 
 * Simple health check service for VR API endpoints
 * This is the first VR service to be deployed for testing
 */

import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';

const app = express();

// Configuration from environment variables
const VR_API_PORT = process.env.VR_API_PORT || 3002;
const VR_API_HOST = process.env.VR_API_HOST || '0.0.0.0';
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://hiyqiqbgiueyyvqoqhht.supabase.co';
const SUPABASE_KEY = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY || 'sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Middleware
app.use(cors({
  origin: ['https://mvs.kanousai.com', 'https://api.mvs.kanousai.com'],
  credentials: true
}));

app.use(express.json());

// Add request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

/**
 * VR Health Check Endpoint
 * GET /health
 */
app.get('/health', async (req, res) => {
  try {
    const healthData = {
      service: 'mvs-vr-api',
      status: 'healthy',
      version: '2.0.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      api_structure: 'simplified',
      endpoints: {
        health: '/vendor/vr/health',
        experiences: '/vendor/vr/experiences',
        assets: '/vendor/vr/assets',
        menus: '/vendor/vr/menus',
        physics: '/vendor/vr/physics',
        haptics: '/vendor/vr/haptics',
        audio: '/vendor/vr/audio',
        saves: '/vendor/vr/saves',
        commerce: '/vendor/vr/commerce',
        updates: '/vendor/vr/updates',
        'spatial-zones': '/vendor/vr/spatial-zones'
      },
      features: {
        simplified_endpoints: true,
        vendor_scoped: true,
        backward_compatibility: false,
        api_key_auth: true,
        rate_limiting: true,
        large_file_support: true,
        asset_caching: true,
        haptic_feedback: true,
        spatial_audio: true,
        physics_simulation: true,
        vr_commerce: true
      },
      hardware_support: {
        varjo_xr4: true,
        leap_motion: true,
        haptic_gloves: true,
        generic_controllers: true
      },
      system_info: {
        node_version: process.version,
        platform: process.platform,
        arch: process.arch,
        memory_usage: process.memoryUsage(),
        pid: process.pid
      }
    };

    // Test database connectivity
    try {
      const { data, error } = await supabase
        .from('vr_experiences')
        .select('id')
        .limit(1);

      if (error && !error.message.includes('permission denied')) {
        healthData.database = {
          status: 'error',
          error: error.message
        };
      } else {
        healthData.database = {
          status: 'connected',
          url: SUPABASE_URL,
          tables_accessible: true
        };
      }
    } catch (dbError) {
      healthData.database = {
        status: 'error',
        error: dbError.message
      };
    }

    // Add response headers
    res.set({
      'X-VR-Service': 'mvs-vr-api',
      'X-VR-Version': '2.0.0',
      'X-API-Structure': 'simplified',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.status(200).json(healthData);

  } catch (error) {
    console.error('Health check error:', error);
    
    res.status(500).json({
      service: 'mvs-vr-api',
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * VR Service Status Endpoint
 * GET /status
 */
app.get('/status', (req, res) => {
  res.json({
    service: 'mvs-vr-api',
    status: 'operational',
    message: 'VR API service is running with simplified endpoint structure',
    simplified_structure: true,
    base_path: '/vendor/vr',
    timestamp: new Date().toISOString()
  });
});

/**
 * Root endpoint
 * GET /
 */
app.get('/', (req, res) => {
  res.json({
    message: 'MVS-VR API Service',
    version: '2.0.0',
    structure: 'simplified',
    base_path: '/vendor/vr',
    documentation: 'https://api.mvs.kanousai.com/docs/vr',
    health_check: '/vendor/vr/health',
    timestamp: new Date().toISOString()
  });
});

/**
 * 404 handler
 */
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'VR API endpoint not found',
    message: `The endpoint ${req.originalUrl} does not exist`,
    available_endpoints: [
      '/vendor/vr/health',
      '/vendor/vr/experiences',
      '/vendor/vr/assets',
      '/vendor/vr/menus'
    ],
    suggestion: 'Check the VR API documentation for available endpoints'
  });
});

/**
 * Error handler
 */
app.use((error, req, res, next) => {
  console.error('VR API Error:', error);
  
  res.status(500).json({
    error: 'VR API internal error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

/**
 * Start the VR health service
 */
const server = app.listen(VR_API_PORT, VR_API_HOST, () => {
  console.log('🚀 MVS-VR Health Service Started');
  console.log('================================');
  console.log(`📡 Service: VR Health API`);
  console.log(`🌐 Host: ${VR_API_HOST}`);
  console.log(`🔌 Port: ${VR_API_PORT}`);
  console.log(`🏗️ Structure: Simplified (/vendor/vr/)`);
  console.log(`🗄️ Database: ${SUPABASE_URL}`);
  console.log(`⚡ Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log('');
  console.log('📋 Available endpoints:');
  console.log(`   GET  http://localhost:${VR_API_PORT}/health`);
  console.log(`   GET  http://localhost:${VR_API_PORT}/status`);
  console.log(`   GET  http://localhost:${VR_API_PORT}/`);
  console.log('');
  console.log('🌐 Public endpoints:');
  console.log(`   GET  https://api.mvs.kanousai.com/vendor/vr/health`);
  console.log(`   GET  https://api.mvs.kanousai.com/vendor/vr/status`);
  console.log('');
  console.log('✅ VR Health Service ready for testing!');
});

/**
 * Graceful shutdown
 */
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down VR Health Service gracefully');
  server.close(() => {
    console.log('✅ VR Health Service stopped');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down VR Health Service gracefully');
  server.close(() => {
    console.log('✅ VR Health Service stopped');
    process.exit(0);
  });
});

export default app;
