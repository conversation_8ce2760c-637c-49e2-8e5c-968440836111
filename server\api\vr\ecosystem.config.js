/**
 * PM2 Ecosystem Configuration for VR API Services
 * Task 6.2.4: VR service dependencies and deployment configuration
 * Date: 2025-07-01
 */

module.exports = {
  apps: [
    {
      // Main VR API Service
      name: 'vr-api-service',
      script: './index.js',
      instances: 2,
      exec_mode: 'cluster',
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        VR_API_PORT: 3002,
        VR_API_BASE_PATH: '/vendor/vr',
        VR_API_HOST: '0.0.0.0',
        VR_SERVICE_NAME: 'vr-api-service'
      },
      
      // Development environment
      env_development: {
        NODE_ENV: 'development',
        VR_API_PORT: 3002,
        VR_DEBUG_ENABLED: true,
        VR_VERBOSE_LOGGING: true,
        VR_TEST_MODE: true
      },
      
      // Staging environment
      env_staging: {
        NODE_ENV: 'staging',
        VR_API_PORT: 3002,
        VR_MONITORING_ENABLED: true,
        VR_PERFORMANCE_ALERTS_ENABLED: true
      },
      
      // Logging configuration
      log_file: '/var/log/pm2/vr-api.log',
      error_file: '/var/log/pm2/vr-api-error.log',
      out_file: '/var/log/pm2/vr-api-out.log',
      merge_logs: true,
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Advanced PM2 options
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Health monitoring
      health_check_url: 'http://localhost:3002/vendor/vr/health',
      health_check_grace_period: 3000,
      
      // Auto-restart conditions
      ignore_watch: [
        'node_modules',
        'logs',
        'temp',
        'cache',
        '.git'
      ],
      
      // Process monitoring
      monitoring: {
        http: true,
        https: false,
        port: 3002
      }
    },
    
    {
      // VR Asset Processing Service (separate process for heavy operations)
      name: 'vr-asset-processor',
      script: './services/asset-processor.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '2G',
      restart_delay: 5000,
      
      env: {
        NODE_ENV: 'production',
        VR_ASSET_PROCESSOR_PORT: 3003,
        VR_ASSET_STORAGE_PATH: '/opt/mvs-vr/assets/vr',
        VR_ASSET_MAX_SIZE: '150MB',
        VR_ASSET_PROCESSING_TIMEOUT: 300000
      },
      
      log_file: '/var/log/pm2/vr-asset-processor.log',
      error_file: '/var/log/pm2/vr-asset-processor-error.log',
      out_file: '/var/log/pm2/vr-asset-processor-out.log',
      merge_logs: true,
      time: true,
      
      // Asset processor specific settings
      kill_timeout: 10000, // Longer timeout for asset processing
      max_restarts: 5,
      min_uptime: '30s'
    },
    
    {
      // VR Background Services (cleanup, backup, monitoring)
      name: 'vr-background-services',
      script: './services/background-services.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 10000,
      
      env: {
        NODE_ENV: 'production',
        VR_BACKGROUND_SERVICES_ENABLED: true,
        VR_CLEANUP_INTERVAL: 3600000, // 1 hour
        VR_BACKUP_INTERVAL: 86400000, // 24 hours
        VR_MONITORING_INTERVAL: 60000  // 1 minute
      },
      
      log_file: '/var/log/pm2/vr-background.log',
      error_file: '/var/log/pm2/vr-background-error.log',
      out_file: '/var/log/pm2/vr-background-out.log',
      merge_logs: true,
      time: true,
      
      // Background service specific settings
      autorestart: true,
      max_restarts: 3,
      min_uptime: '60s'
    }
  ],
  
  // PM2 deployment configuration
  deploy: {
    production: {
      user: 'root',
      host: '**************',
      ref: 'origin/main',
      repo: 'https://github.com/mvs-vr/mvs-vr-v2.git',
      path: '/opt/mvs-vr',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'root',
      host: '**************',
      ref: 'origin/staging',
      repo: 'https://github.com/mvs-vr/mvs-vr-v2.git',
      path: '/opt/mvs-vr-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
