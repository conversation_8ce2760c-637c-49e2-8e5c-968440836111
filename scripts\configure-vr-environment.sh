#!/bin/bash

# Configure VR Environment Variables
# Task 6.2.3: Configure VR environment variables
# Date: 2025-07-01

echo "⚙️ VR Environment Variables Configuration"
echo "Task: 6.2.3 - Configure VR environment variables"
echo "=============================================="

# Configuration paths
VR_ENV_FILE="/opt/mvs-vr/.env.vr"
SYSTEM_ENV_FILE="/etc/environment"
SERVICE_ENV_DIR="/etc/systemd/system"

echo ""
echo "📝 Creating VR environment configuration..."

# Create VR-specific environment file
echo "   Creating VR environment file: $VR_ENV_FILE"
cat > "$VR_ENV_FILE" << 'EOF'
# MVS-VR Environment Variables Configuration
# Generated: 2025-07-01

# VR Service Configuration
VR_API_PORT=3002
VR_API_BASE_PATH=/vendor/vr
VR_API_HOST=0.0.0.0
VR_API_TIMEOUT=300000

# VR Asset Storage Configuration
VR_ASSET_STORAGE_PATH=/opt/mvs-vr/assets/vr
VR_ASSET_MAX_SIZE=150MB
VR_ASSET_CACHE_DURATION=3600
VR_ASSET_PRELOAD_ENABLED=true

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP

# VR Authentication
VR_API_KEY_HEADER=X-VR-API-Key
VR_SESSION_TIMEOUT=7200
VR_RATE_LIMIT_REQUESTS=100
VR_RATE_LIMIT_WINDOW=60

# VR Hardware Configuration
VR_HEADSET_TYPE=varjo_xr4
VR_CONTROLLER_TYPE=leap_motion
VR_HAPTICS_ENABLED=true

# VR Performance
VR_MAX_ELEMENTS_PER_SCENE=30
VR_MAX_CONCURRENT_USERS=50
VR_ASSET_PRELOAD_COUNT=30

# VR Features
VR_FEATURE_HAPTICS=true
VR_FEATURE_SPATIAL_AUDIO=true
VR_FEATURE_PHYSICS=true
VR_FEATURE_COMMERCE=true

# VR Logging
VR_LOG_LEVEL=info
VR_LOG_FILE=/var/log/mvs-vr/vr-service.log

# VR Security
VR_CORS_ORIGINS=https://mvs.kanousai.com,https://api.mvs.kanousai.com
VR_CSRF_PROTECTION_ENABLED=true

# UE Plugin Integration
UE_PLUGIN_API_ENDPOINT=https://api.mvs.kanousai.com/vendor/vr
UE_PLUGIN_AUTH_METHOD=api_key
EOF

echo "   Setting permissions for VR environment file..."
chmod 640 "$VR_ENV_FILE"
chown root:www-data "$VR_ENV_FILE"

echo ""
echo "🔧 Creating VR service environment configuration..."

# Create systemd environment file for VR service
cat > "$SERVICE_ENV_DIR/vr-api.service.d/environment.conf" << 'EOF'
[Service]
EnvironmentFile=/opt/mvs-vr/.env.vr
Environment=NODE_ENV=production
Environment=VR_SERVICE_NAME=vr-api
EOF

echo ""
echo "📋 Creating VR environment validation script..."

# Create environment validation script
cat > "/opt/mvs-vr/scripts/validate-vr-env.sh" << 'EOF'
#!/bin/bash
# VR Environment Validation Script

echo "🔍 Validating VR Environment Configuration..."

# Source VR environment
source /opt/mvs-vr/.env.vr

# Required VR variables
REQUIRED_VARS=(
    "VR_API_PORT"
    "VR_API_BASE_PATH"
    "VR_ASSET_STORAGE_PATH"
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY"
    "VR_HEADSET_TYPE"
    "VR_CONTROLLER_TYPE"
)

echo ""
echo "📊 Checking required VR environment variables..."

ALL_VALID=true
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "   ❌ $var: NOT SET"
        ALL_VALID=false
    else
        echo "   ✅ $var: ${!var}"
    fi
done

echo ""
echo "🔍 Checking VR asset storage paths..."

VR_PATHS=(
    "$VR_ASSET_STORAGE_PATH"
    "$VR_ASSET_STORAGE_PATH/models"
    "$VR_ASSET_STORAGE_PATH/textures"
    "$VR_ASSET_STORAGE_PATH/cache"
    "$VR_ASSET_STORAGE_PATH/temp"
)

for path in "${VR_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "   ✅ $path: EXISTS"
    else
        echo "   ❌ $path: MISSING"
        ALL_VALID=false
    fi
done

echo ""
echo "🌐 Testing VR database connectivity..."

# Test Supabase connection (basic check)
if command -v curl &> /dev/null; then
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/" -H "apikey: $NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY")
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "401" ]; then
        echo "   ✅ Supabase connection: REACHABLE (HTTP $HTTP_CODE)"
    else
        echo "   ❌ Supabase connection: FAILED (HTTP $HTTP_CODE)"
        ALL_VALID=false
    fi
else
    echo "   ⚠️ Supabase connection: CANNOT TEST (curl not available)"
fi

echo ""
if [ "$ALL_VALID" = true ]; then
    echo "✅ VR Environment Validation: PASSED"
    exit 0
else
    echo "❌ VR Environment Validation: FAILED"
    exit 1
fi
EOF

chmod +x "/opt/mvs-vr/scripts/validate-vr-env.sh"

echo ""
echo "🔍 Validating VR environment configuration..."

# Run validation
if /opt/mvs-vr/scripts/validate-vr-env.sh; then
    echo ""
    echo "✅ Task 6.2.3 COMPLETED: VR environment variables configured successfully!"
    echo ""
    echo "📁 Configuration Files Created:"
    echo "   🔧 $VR_ENV_FILE"
    echo "   ⚙️ $SERVICE_ENV_DIR/vr-api.service.d/environment.conf"
    echo "   🔍 /opt/mvs-vr/scripts/validate-vr-env.sh"
    echo ""
    echo "🚀 Ready to proceed with Task 6.2.4: Install VR service dependencies"
    exit 0
else
    echo ""
    echo "❌ Task 6.2.3 FAILED: VR environment validation failed"
    echo "⚠️ Please check the errors above and fix the configuration"
    exit 1
fi
