#!/usr/bin/env node

/**
 * Consolidated MVS-VR Vendor Service
 * Single endpoint for all vendor functionality
 */

const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3005;

app.use(express.json());
app.use(express.static('public'));

// CORS for vendor service
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'https://mvs.kanousai.com');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});

// Vendor Portal HTML Template
const vendorPortalHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVS-VR Vendor Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .vendor-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .vendor-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .vendor-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .vendor-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .vendor-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .vendor-menu a:hover {
            background: #667eea;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            background: #667eea;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .card-description {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .card-action {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            transition: background 0.3s ease;
        }
        
        .card-action:hover {
            background: #5a6fd8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .vendor-nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .vendor-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="vendor-header">
        <nav class="vendor-nav">
            <div class="vendor-logo">MVS-VR Vendor Portal</div>
            <ul class="vendor-menu">
                <li><a href="#dashboard">Dashboard</a></li>
                <li><a href="#products">Products</a></li>
                <li><a href="#showrooms">Showrooms</a></li>
                <li><a href="#orders">Orders</a></li>
                <li><a href="#analytics">Analytics</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="welcome-card">
            <h1>Welcome to Your Vendor Portal</h1>
            <p><span class="status-indicator"></span>Single consolidated endpoint for all vendor functions</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card" onclick="openProducts()">
                <div class="card-icon">📦</div>
                <h3 class="card-title">Product Management</h3>
                <p class="card-description">Manage your product catalog and VR assets</p>
            </div>

            <div class="dashboard-card" onclick="openShowrooms()">
                <div class="card-icon">🏪</div>
                <h3 class="card-title">Virtual Showrooms</h3>
                <p class="card-description">Design and manage your virtual showrooms</p>
            </div>

            <div class="dashboard-card" onclick="openOrders()">
                <div class="card-icon">🛒</div>
                <h3 class="card-title">Order Management</h3>
                <p class="card-description">Track orders and manage inventory</p>
            </div>

            <div class="dashboard-card" onclick="openAnalytics()">
                <div class="card-icon">📊</div>
                <h3 class="card-title">Analytics & Reports</h3>
                <p class="card-description">Monitor performance and sales data</p>
            </div>
        </div>
    </div>

    <script>
        function openProducts() {
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/products';
        }

        function openShowrooms() {
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/showrooms';
        }

        function openOrders() {
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/content/orders';
        }

        function openAnalytics() {
            window.location.href = 'https://api.mvs.kanousai.com/directus/admin/insights';
        }

        console.log('MVS-VR Consolidated Vendor Portal loaded');
    </script>
</body>
</html>
`;

// Routes

// Main vendor portal interface
app.get('/', (req, res) => {
  res.send(vendorPortalHTML);
});

// Health endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'consolidated-vendor-service',
    timestamp: new Date().toISOString(),
    endpoints: ['/', '/health', '/api/register', '/api/profile', '/api/stats']
  });
});

// Vendor registration API (legacy compatibility)
app.post('/api/register', (req, res) => {
  const { email, company, phone } = req.body;
  
  if (!email || !company) {
    return res.status(400).json({ 
      error: 'Email and company name are required' 
    });
  }
  
  res.json({
    success: true,
    message: 'Vendor registration received',
    data: {
      email,
      company,
      phone,
      status: 'pending_approval',
      id: Date.now()
    }
  });
});

// Vendor profile API
app.get('/api/profile', (req, res) => {
  res.json({
    id: 1,
    name: 'Sample Vendor',
    email: '<EMAIL>',
    company: 'Sample Company',
    status: 'active',
    products_count: 12,
    showrooms_count: 3,
    orders_count: 45,
    created_at: '2025-01-01T00:00:00Z'
  });
});

// Vendor stats API
app.get('/api/stats', (req, res) => {
  res.json({
    total_products: 12,
    active_showrooms: 3,
    pending_orders: 5,
    monthly_revenue: 15420,
    vr_sessions: 234,
    conversion_rate: 12.5,
    last_updated: new Date().toISOString()
  });
});

// Legacy health endpoint for compatibility
app.get('/status', (req, res) => {
  res.json({
    status: 'ok',
    service: 'vendor-registration-api'
  });
});

// Start consolidated vendor service
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`🏪 MVS-VR Consolidated Vendor Service running on port ${PORT}`);
    console.log(`📱 Portal: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/api/*`);
    console.log(`✅ Single endpoint for all vendor functionality`);
  });
}

module.exports = app;
